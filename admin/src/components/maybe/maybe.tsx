import React, { PropsWithChildren } from "react";

type Condition = boolean | (() => boolean);
type MaybeProps = {
  condition: Condition;
};

const resolve = (condition: Condition): boolean => (typeof condition === "function" ? condition() : condition);

export default function Maybe({ condition, children }: PropsWithChildren<MaybeProps>): JSX.Element {
  return <>{resolve(condition) ? children : null}</>;
}
