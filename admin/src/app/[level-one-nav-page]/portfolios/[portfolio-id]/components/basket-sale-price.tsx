import { Box } from "@mui/material";
import React, { useState } from "react";
import { KeyedMutator } from "swr";
import Decimal from "decimal.js";
import { AdminBookResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import COLORS from "@components/ui/theme/colors";
import CustomButton from "@components/ui/custom-button/custom-button";
import BookPriceModal from "./portfolio-price-modal";

const btnStyle = {
  borderRadius: 2,
  backgroundColor: COLORS.normalGray,
  fontSize: "14px",
  fontWeight: 600,
  color: "rgba(30, 70, 57, 1)",
  marginLeft: "20px",
  marginTop: "-3px",
  "&:hover": {
    backgroundColor: COLORS.whiteGrey,
  },
  minWidth: "90px",
  padding: "5px",
  border: "solid",
  borderWidth: "1px",
  borderColor: "rgba(0, 0, 0, 0.23)",
  boxShadow: "none",
  textTransform: "capitalize",
};

interface BasketSalePriceProps {
  book: AdminBookResponse;
  algoPrice?: Decimal;
  algoPriceWithRisk?: Decimal;
  refresh: KeyedMutator<AdminBookResponse>;
}

export default function BasketSalePrice(props: BasketSalePriceProps): JSX.Element {
  const { book, refresh, algoPrice, algoPriceWithRisk } = props;
  const [isEditPriceDialogOpen, setIsEditPriceDialogOpen] = useState<boolean>(false);
  const [currentBasketPrice, setCurrentBasketPrice] = useState<number>(+(book.purchasePrice ?? 0));
  const editBasketPriceHandler = (): void => {
    setCurrentBasketPrice(+(book.purchasePrice ?? 0));
    setIsEditPriceDialogOpen(true);
  };

  function saveUpdatedPriceHandler(): void {
    setIsEditPriceDialogOpen(false);
    refresh();
  }

  function closeEditPriceHandler(): void {
    setIsEditPriceDialogOpen(false);
  }

  return (
    <Box
      sx={{
        borderRadius: "4px",
        display: "flex",
      }}
    >
      <CustomButton
        onClickHandler={editBasketPriceHandler}
        requiredPermission={PermissionEnum.TRANSFERS_EXECUTE}
        style={btnStyle}
      >
        Set Price
      </CustomButton>
      <BookPriceModal
        currentBasketPrice={currentBasketPrice}
        currentRiskAdjPrice={book?.purchasePriceWithBuffer ? +book?.purchasePriceWithBuffer : null}
        algoPrice={algoPrice}
        algoPriceWithRisk={algoPriceWithRisk}
        book={book}
        bookId={book?.id}
        isOpen={isEditPriceDialogOpen}
        onSave={saveUpdatedPriceHandler}
        onClose={closeEditPriceHandler}
      />
    </Box>
  );
}
