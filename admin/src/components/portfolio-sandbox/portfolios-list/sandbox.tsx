import React, { useState, useEffect, useCallback } from "react";
import { ModelPortfolioQueryResponse, ModelPortfolioStatus, PermissionEnum, uuid } from "@rubiconcarbon/shared-types";
import { stringComparator } from "@/utils/comparators/comparator";
import EnhancedTable, { ColDef, SortOrder } from "../../ui/table/enhanced-table";
import TableBox from "../../ui/table-box/table-box";
import ActionButton from "@/components/ui/action-button/action-button-enhanced";
import { Stack } from "@mui/material";
import { SandboxPortfolio, mapSandboxPortfolio } from "@/models/sandbox-portfolio";
import dateFormatterEST from "@/utils/formatters/estDateFormatter";
import PortfolioSandboxActions from "./sandbox-actions";
import NewPortfolioModal from "../portfolio-sandbox-header/new-portfolio-modal";
import PortfolioStatus from "./portfolio-status";
import OrganizationName from "@/components/ui/organization-name/organization-name";
import { KeyedMutator } from "swr";
import { useRouter } from "next/router";
import { NO_STATUS, PortfolioStatusMapping } from "@/mappers/portfolio-status-mapper";

export default function PortfolioSandbox(props: {
  ModelPortfolios: ModelPortfolioQueryResponse;
  mutate: KeyedMutator<ModelPortfolioQueryResponse>;
}): JSX.Element {
  const { ModelPortfolios, mutate } = props;
  const [portfolios, setPortfolios] = useState<SandboxPortfolio[]>();
  const [isNewPortfolioDialogOpen, setIsNewPortfolioDialogOpen] = useState<boolean>(false);
  const { push } = useRouter();

  const getActions = useCallback(
    (inputMap: Map<string, string | boolean>): JSX.Element => {
      return (
        <PortfolioSandboxActions
          id={inputMap.get("id").toString()}
          name={inputMap.get("name").toString()}
          allowExportPDF={inputMap.get("allowExportPDF") === true ? true : false}
          refreshPortfolios={mutate}
        />
      );
    },
    [mutate],
  );

  const getCustomer = useCallback((inputMap: Map<string, string>): JSX.Element => {
    return (
      <OrganizationName
        organization={{ name: inputMap.get("organizationName"), id: inputMap.get("organizationId") } as any}
        style={{ fontSize: 14 }}
      />
    );
  }, []);

  const getStatus = useCallback(
    (inputMap: Map<string, string>): JSX.Element => {
      const id = inputMap.get("id").toString();
      const status = inputMap.get("status");
      return (
        <PortfolioStatus portfolioId={id} currentStatus={status as ModelPortfolioStatus} onSuccess={() => mutate()} />
      );
    },
    [mutate],
  );

  const columnsDef: ColDef[] = [
    {
      columnName: "uiKey",
      displayName: "Portfolio Id",
      comparator: stringComparator,
      style: { width: "200px" },
    },
    {
      columnName: "name",
      displayName: "Portfolio Name",
      comparator: stringComparator,
    },
    {
      columnName: "organizationName",
      displayName: "Customer",
      comparator: stringComparator,
      formatter: {
        func: getCustomer,
        inputFields: ["organizationName", "organizationId"],
      },
    },
    {
      columnName: "status",
      displayName: "Status",
      sortable: false,
      style: { width: "150px" },
      formatter: {
        func: getStatus,
        inputFields: ["status", "id"],
        overrideMissingDataDisplay: true,
      },
    },
    {
      columnName: "lastUpdated",
      displayName: "Last Updated",
      formatter: { func: dateFormatterEST },
      exportFormatter: { func: dateFormatterEST },
    },
    {
      columnName: "createdBy",
      displayName: "Created By",
      comparator: stringComparator,
    },
    {
      columnName: "updatedBy",
      displayName: "Updated By",
      comparator: stringComparator,
    },
    {
      columnName: "id",
      displayName: "Actions",
      formatter: {
        func: getActions,
        inputFields: ["id", "name", "allowExportPDF"],
      },
      exportable: false,
      sortable: false,
    },
  ];

  useEffect(() => {
    if (ModelPortfolios && ModelPortfolios.data) {
      ModelPortfolios.data.sort((a, b) => (a.createdAt < b.createdAt ? 1 : -1));
      setPortfolios(mapSandboxPortfolio(ModelPortfolios.data));
    }
  }, [ModelPortfolios]);

  const isStatusMatch = useCallback((input: string, status: ModelPortfolioStatus): boolean => {
    if (
      (status === undefined && NO_STATUS.toUpperCase()?.includes(input)) ||
      PortfolioStatusMapping[status]?.toUpperCase()?.includes(input)
    )
      return true;

    return false;
  }, []);

  const getFilteredData = useCallback(
    (input: string): void => {
      const searchString = input.toUpperCase();
      const filteredData = ModelPortfolios?.data.filter(
        (ModelPortfolio) =>
          ModelPortfolio?.uiKey?.toUpperCase().includes(searchString) ||
          ModelPortfolio.name.toUpperCase().includes(searchString) ||
          ModelPortfolio.createdBy.name.toUpperCase().includes(searchString) ||
          isStatusMatch(searchString, ModelPortfolio?.status) ||
          ModelPortfolio.updatedBy.name.toUpperCase().includes(searchString),
      );
      setPortfolios(mapSandboxPortfolio(filteredData));
    },
    [ModelPortfolios?.data, isStatusMatch],
  );

  const newPortfolioHandler = (): void => {
    setIsNewPortfolioDialogOpen(true);
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <Stack direction="row" gap="15px">
        <ActionButton
          onClickHandler={newPortfolioHandler}
          requiredPermission={PermissionEnum.MODEL_PORTFOLIOS_CREATE}
          style={{ width: "190px" }}
        >
          Create Portfolio
        </ActionButton>
      </Stack>
    );
  };

  const saveNewPortfolioHandler = (id: uuid): void => {
    setIsNewPortfolioDialogOpen(false);
    if (!!id) {
      push(`/inventory-management/portfolio-sandbox/${id}/edit-portfolio-sandbox`);
    }
  };

  const closeNewPortfolioHandler = (): void => {
    setIsNewPortfolioDialogOpen(false);
  };

  return (
    <>
      <TableBox>
        {portfolios && (
          <EnhancedTable
            name={"portfolios_info"}
            columnsDef={columnsDef}
            exportable={true}
            data={portfolios}
            rowsCountPerPage={100}
            getFilteredData={getFilteredData}
            searchBarContent={getSearchBarContent}
            defaultSort={{ columnName: "lastUpdated", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
      <NewPortfolioModal
        isOpen={isNewPortfolioDialogOpen}
        onSave={saveNewPortfolioHandler}
        onClose={closeNewPortfolioHandler}
      />
    </>
  );
}
