import {
  BookAction,
  BookType,
  ForwardLineItemResponse,
  ForwardOrganizationResponse,
  ForwardResponse,
  ForwardStatus,
  ForwardType,
  OrganizationType,
  TrimmedBookResponse,
  TrimmedOrganizationResponse,
  uuid,
} from "@rubiconcarbon/shared-types";
import { Type } from "class-transformer";
import { IsDate, IsNotEmpty, IsPositive, MaxDate, MinDate, ValidateIf, ValidateNested } from "class-validator";
import { <PERSON>Fields, <PERSON>OfField, MinOfField } from "../utilities/number-validators";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import { IsNotEmptyString } from "../utilities/string-validators";
import { PrimitiveTypeProject } from "@models/primitive-type-project";
import { PrimitiveTypeVintage } from "@models/primitive-type-vintage";

const minDate = new Date();
minDate.setHours(0, 0, 0, 0);

const maxDate = new Date();
maxDate.setHours(23, 59, 0, 0);

class ForwardBook implements TrimmedBookResponse {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid;

  allowedActions: BookAction[];

  description?: string;

  isEnabled: boolean;

  name: string;

  organization: TrimmedOrganizationResponse;

  type: BookType;

  createdAt: Date;

  updatedAt?: Date;
}

class OrganizationBook implements ForwardOrganizationResponse {
  constructor() {}

  @IsNotEmpty({ message: "Required" })
  id: uuid;

  type: OrganizationType;

  name: string;

  createdAt: Date;

  updatedAt?: Date;
}

export class ForwardLineItem
  implements
    Omit<
      ForwardLineItemResponse,
      | "expectedAmount"
      | "lastUpdatedDeliveryDate"
      | "maxAmount"
      | "minAmount"
      | "rawPrice"
      | "serviceFee"
      | "settledAmount"
      | "otherFee"
      | "originalDeliveryDate"
      | "settledAt"
      | "createdAt"
      | "updatedAt"
      | "projectVintage" //TODO:: Fix build error
    >
{
  constructor() {}

  id: uuid;

  _id: string; // for auto-generating render ids

  uiKey: string;

  @ValidateIf((o: ForwardLineItem) => o?.stagedForDelivery)
  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => ForwardBook)
  book?: ForwardBook;

  @ValidateIf((o: ForwardLineItem) => !o?.stagedForDelivery)
  @BetweenFields<ForwardLineItem>("minAmount", "maxAmount", {
    message: "Please enter a quantity between Min and Max",
  })
  @IsNotEmpty({ message: "Required" })
  expectedAmount: string; // number

  @ValidateIf((o: GenericTableRowModel<ForwardLineItem>) => !o?.creating && !o.stagedForDelivery)
  @MinDate(minDate, { message: "Date cannot be in the past" })
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  lastUpdatedDeliveryDate: string; // date;

  lineItemKey: string;

  @ValidateIf((o: ForwardLineItem) => !o?.stagedForDelivery)
  @MinOfField<ForwardLineItem>("minAmount", { message: "Max should be more than Min quantity" })
  @IsNotEmpty({ message: "Required" })
  maxAmount: string; // number

  @ValidateIf((o: ForwardLineItem) => !o?.stagedForDelivery)
  @MaxOfField<ForwardLineItem>("maxAmount", { message: "Min should not exceed Max quantity" })
  @IsNotEmpty({ message: "Required" })
  minAmount: string; // number

  @ValidateIf((o: GenericTableRowModel<ForwardLineItem>) => !!o?.creating)
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  originalDeliveryDate: string; // date

  @Type(() => Number)
  @ValidateIf((o) => !o.price)
  @IsPositive({ message: "Please enter a positive value" })
  @IsNotEmpty({ message: "Required if Sub Total is empty" })
  unitPrice: string; // decimal

  @Type(() => Number)
  @ValidateIf((o) => !o.unitPrice)
  @IsPositive({ message: "Please enter a positive value" })
  @IsNotEmpty({ message: "Required if Price is empty" })
  rawPrice: string; // decimal

  @ValidateNested()
  @Type(() => PrimitiveTypeVintage)
  projectVintage: PrimitiveTypeVintage;

  otherFee?: string; // decimal

  serviceFee?: string; // decimal

  feeTotal?: string; // decimal

  grandTotal?: string; // decimal

  @ValidateIf((o: ForwardLineItem) => o?.stagedForDelivery)
  @BetweenFields<ForwardLineItem>("minAmount", "maxAmount", {
    message: "Please enter a quantity between Min and Max",
  })
  @IsNotEmpty({ message: "Required" })
  settledAmount?: string; // number

  status: ForwardStatus = ForwardStatus.PENDING;

  @ValidateIf((o: ForwardLineItem) => o?.stagedForDelivery)
  @MaxDate(maxDate, { message: "Date cannot be in the future" })
  @IsDate({ message: "Invalid Date" })
  @IsNotEmpty({ message: "Required" })
  @Type(() => Date)
  settledAt: string; // date

  stagedForDelivery: boolean;

  bookBreached: boolean;

  availableAmount?: number;

  docsCount: number;
}

export class ForwardModel
  implements Omit<ForwardResponse, "amount" | "organization" | "lineItems" | "createdAt" | "updatedAt" | "project">
{
  constructor() {}

  id: uuid;

  uiKey: string;

  @ValidateNested()
  @Type(() => PrimitiveTypeProject)
  project: PrimitiveTypeProject;

  @IsNotEmptyString({ message: "Cannot be an empty string" })
  @IsNotEmpty({ message: "Required" })
  @ValidateIf((o) => !!o.creating && (o.type === undefined || o.type === ForwardType.BUY))
  counterparty: string;

  @ValidateIf((o) => o.creating && o.type === "sell")
  @ValidateNested()
  @IsNotEmpty({ message: "Required" })
  @Type(() => ForwardBook)
  customerPortfolio: ForwardBook;

  @Type(() => OrganizationBook)
  organization: OrganizationBook;

  memo: string;

  documents: string;

  amount: string; // number

  status: ForwardStatus = ForwardStatus.PENDING; // by default, it should pending

  lineItems: ForwardLineItem[] = [];

  @IsNotEmpty({ message: "Required" })
  type: ForwardType;

  createdAt: string; // date

  updatedAt: string; // date
}

export class ForwardFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => ForwardModel)
  amends: ForwardModel[];
}

export class MultiForwardLineItemFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => ForwardLineItem)
  amends: ForwardLineItem[];
}
