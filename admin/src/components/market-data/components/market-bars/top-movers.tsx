import { Chip, Stack, Typography } from "@mui/material";
import { useEffect, useState, useContext, useCallback } from "react";
import MarketIndexDisplay from "./market-index-display";
import classes from "./styles.module.scss";
import { VintagePricingResponse } from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@/providers/axios-provider";
import Marquee from "react-fast-marquee";
import { isEmpty } from "lodash";
import Maybe from "@/components/maybe/maybe";

const TopMovers = (props: { filter: string }): JSX.Element => {
  const { filter } = props;
  const [vintagePricing, setVintagePricing] = useState<any[]>([]);
  const { api } = useContext(AxiosContext);

  const getVintagePricing = useCallback(async () => {
    const responsePrice = await api.post<VintagePricingResponse[]>(
      `reporting/vintage-pricing?source=viridios&sort_order=${filter}`,
    );
    if (!isEmpty(responsePrice?.data)) {
      setVintagePricing(responsePrice.data);
    }
  }, [api, filter]);

  useEffect(() => {
    getVintagePricing();
  }, [getVintagePricing]);

  return (
    <Stack direction="row" gap={1} alignItems="center" width="100%">
      <Typography variant="body2" component="div" className={classes.moversTitle}>
        Top Movers
        <Chip label="By Viridios" sx={{ marginLeft: "5px", height: "24px" }} /> :
      </Typography>
      <Maybe condition={!isEmpty(vintagePricing)}>
        <Marquee pauseOnHover={true}>
          <Stack direction="row" gap={2} alignItems="center">
            {vintagePricing?.map((vp, idx) => (
              <MarketIndexDisplay
                key={idx}
                label={`${vp.project_id} - ${vp.vintage}`}
                percent={vp[filter]}
                price={vp.price}
                tooltip={vp?.project_name ?? ""}
              />
            ))}
          </Stack>
        </Marquee>
      </Maybe>
    </Stack>
  );
};

export default TopMovers;
