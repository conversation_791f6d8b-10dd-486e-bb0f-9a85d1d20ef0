import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";
import { numberFormat, useRequest, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import {
  AdminRetirementQuery,
  AdminRetirementQueryResponse,
  PermissionEnum,
  RetirementRelations,
  RetirementStatus,
  RetirementType,
  SuggestedBufferElement,
  SuggestedBufferRetirementRequest,
  SuggestedBufferRetirementResponse,
} from "@rubiconcarbon/shared-types";
import { useLogger } from "@/providers/logging";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import GenericTable from "@/components/ui/generic-table";
import { GenericTableColumn } from "@/components/ui/generic-table/types/generic-table-column";
import OrganizationName from "@/components/ui/organization-name/organization-name";
import ProductName from "@/components/ui/product-name/product-name";
import { TransactionModel } from "@/models/transaction";
import dateFormatter from "@/utils/formatters/dateFormatter";
import { toRetirementModel } from "@/utils/helpers/transaction/to-transaction-models";
import { GenericTableFieldSizeEnum } from "@/components/ui/generic-table/constants/generic-table-field-size.enum";
import useGenericTableUtility from "@/components/ui/generic-table/hooks/use-generic-table-utility";
import MatIcon from "@/components/ui/mat-icon/mat-icon";
import { DEFAULT_EXPORT_STYLE } from "@/components/ui/generic-table/constants/generic-table-styles";
import { SyntheticEvent, useMemo, useState } from "react";
import { GenericTableRowModel } from "@/components/ui/generic-table/types/generic-table-row-model";
import { CsvBuilder } from "filefy";
import Link from "next/link";
import { Typography } from "@mui/material";
import COLORS from "@/components/ui/theme/colors";

const COLUMNS: GenericTableColumn<TransactionModel>[] = [
  {
    field: "uiKey",
    label: "Retirement ID",
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => (
      <Link
        href={`/retirements/${row?.id}`}
        style={{ color: COLORS.rubiconGreen, textUnderlineOffset: 4 }}
        onClick={(event: SyntheticEvent) => {
          event.stopPropagation();
        }}
      >
        <Typography variant="body2" component="div" sx={{ fontSize: 14 }}>
          {row?.uiKey}
        </Typography>
      </Link>
    ),
  },
  {
    field: "retirement.productTypeF",
    label: "Type",
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
  },
  {
    field: "retirement.organization.name",
    label: "Customer",
    width: GenericTableFieldSizeEnum.small,
    maxWidth: GenericTableFieldSizeEnum.flexmedium,
    renderDataCell: (row): JSX.Element => (
      <OrganizationName organization={row?.retirement?.organization} style={{ fontSize: 14, fontWeight: 300 }} />
    ),
  },
  {
    field: "product",
    label: "Product",
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    renderDataCell: (row) => <ProductName assets={row?.assets} style={{ fontSize: 14 }} />,
  },
  {
    field: "amount",
    label: "Quantity",
    type: "number",
    width: GenericTableFieldSizeEnum.small,
    maxWidth: GenericTableFieldSizeEnum.flexsmall,
  },
  {
    field: "dateFinished",
    label: "Complete Date",
    type: "date",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    deriveDataValue: (row) => dateFormatter(row?.dateFinished?.toString(), "MM/dd/yyyy hh:mm:ss a"),
  },
];

const BUFFER_CALCULATION_COLUMNS = ["Project ID", "Project Name", "Vintage", "Amount"];

const extractBufferedCalculations = (suggestedBufferElements: SuggestedBufferElement[]): string[][] => {
  return suggestedBufferElements?.map(({ projectVintage, amount }) => [
    projectVintage?.project?.registryProjectId,
    projectVintage?.project?.name,
    projectVintage?.name,
    numberFormat(amount),
  ]);
};

const BufferCalculator = (): JSX.Element => {
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const [selectedRows, setSelectedRows] = useState<GenericTableRowModel<TransactionModel>[]>([]);

  const hasSelection = useMemo(() => selectedRows.length > 0, [selectedRows]);

  const { data: completedRetirementResponse, isLoading } = useRequest<
    AdminRetirementQueryResponse,
    object,
    object,
    AdminRetirementQuery
  >({
    url: "admin/retirements",
    queryParams: {
      includeTotalCount: true,
      limit: SERVER_PAGINATION_LIMIT,
      statuses: [RetirementStatus.COMPLETED],
      types: [RetirementType.RETIREMENT],
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
      ],
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to fetch completed retirements");
        logger.error(`Unable to fetch completed retirements: ${error?.message}`, {});
      },
    },
  });

  const { trigger: runRetirementsBuffer, isMutating: calculating } = useTriggerRequest<
    SuggestedBufferRetirementResponse,
    object,
    object,
    SuggestedBufferRetirementRequest
  >({
    url: "admin/retirements/buffers",
    queryParams: {
      retirementIds: selectedRows.map((row) => row.id),
    },
    swrOptions: {
      onSuccess: (data) => {
        const rows = extractBufferedCalculations(
          data?.suggestedAllocations?.flatMap((allocations) => allocations.suggestedBufferRetirements),
        );

        new CsvBuilder(`RetirementBufferCalculation_${new Date()}.csv`)
          .setColumns(BUFFER_CALCULATION_COLUMNS)
          .addRows(rows)
          .exportFile();
      },
      onError: (error: any) => {
        enqueueError("Unable to run buffer calculation");
        logger.error(`Unable to run buffer calculation: ${error?.data?.message}`, {});
      },
    },
  });

  const { table } = useGenericTableUtility<TransactionModel>({});

  const runCalculation = (): void => {
    runRetirementsBuffer();
  };

  return (
    <GenericTable
      id="Retirements"
      loading={isLoading}
      reloadingRow={isLoading}
      pageableData={completedRetirementResponse}
      columns={COLUMNS}
      toRowModel={toRetirementModel}
      globalSearch={{
        searchKeys: ["uiKey", "retirement.productTypeF", "retirement.organization.name", "product", "dateFinished"],
      }}
      sort={{
        sorts: {
          dateFinished: "desc",
        },
      }}
      export={{
        filename: `CompletedRetirements-${new Date()}`,
        setClientCanExport: table?.setClientCanExport,
        bindClientExport: table?.bindClientExport,
      }}
      styles={{
        root: {
          maxHeight: "calc(100vh - 155px)",
        },
      }}
      toolbarActionButtons={[
        {
          children: "Export",
          startIcon: <MatIcon value="file_download" variant="round" size={18} color="action" />,
          requiredPermission: PermissionEnum.RETIREMENTS_READ,
          style: DEFAULT_EXPORT_STYLE,
          isDisabled: isLoading || !table?.clientCanExport || calculating,
          onClickHandler: table?.handleClientExport,
        },
        {
          children: "Run Calculator",
          startIcon: <MatIcon value="calculate" variant="round" size={18} color="inherit" />,
          requiredPermission: PermissionEnum.RETIREMENTS_READ,
          isDisabled: isLoading || !hasSelection || calculating,
          tooltip: !hasSelection ? "Please select at least one row" : "Run Buffer Calculator",
          onClickHandler: runCalculation,
        },
      ]}
      isSelectable
      eager={{ select: true, unselect: true }}
      onSelectionChange={async (rows) => setSelectedRows(rows)}
    />
  );
};

export default BufferCalculator;
