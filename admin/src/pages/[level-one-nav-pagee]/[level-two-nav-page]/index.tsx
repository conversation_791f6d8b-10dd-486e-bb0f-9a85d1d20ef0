import Page from "@/components/layout/containers/page";
import NestedMenuSection from "@/components/nested-menu-section/nested-menu-section";
import useNavigationMenu from "@/providers/navigation-menu-provider";
import { useRouter } from "next/router";

export default function LevelTwoNavPage(): JSX.Element {
  const { asPath } = useRouter();
  const { permissibleMenus, getPermissableMenu } = useNavigationMenu();
  const menu = getPermissableMenu(asPath, permissibleMenus, "equals");

  return (
    <Page>
      <NestedMenuSection menus={[menu]} />
    </Page>
  );
}
