import { GenericRecord } from "@rubiconcarbon/frontend-shared";

export type Method = "head" | "get" | "post" | "put" | "patch" | "delete";

/**
 * The function `generateQueryParams` creates a query string from an object of key-value pairs.
 * @param {GenericRecord} params - The `generateQueryParams` function takes an object `params` as input
 * and generates a query string by encoding the keys and values of the object. Each key-value pair is
 * encoded using `encodeURIComponent` and then joined together with "&" as the separator.
 */
export const generateQueryParams = (params: GenericRecord = {}): string =>
  Object.keys(params)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key]?.toString())}`)
    .join("&");

/**
 * The function `urlWithPathParams` replaces path parameters in a URL with values from a given object.
 * @param {string} url - The `url` parameter is a string representing the URL with placeholders for
 * path parameters enclosed in curly braces, such as `{param1}`, `{param2}`, etc.
 * @param {GenericRecord} params - The `urlWithPathParams` function takes in a URL string and a params
 * object, which contains key-value pairs for path parameters to be replaced in the URL. The function
 * iterates over the keys in the params object and replaces the corresponding placeholders in the URL
 * with the values from the params object.
 */
export const urlWithPathParams = (url: string, params: GenericRecord = {}): string =>
  Object.keys(params).reduce((prev, curr) => {
    return prev.replace(`{${curr}}`, String(params[curr]));
  }, url);

/**
 * The function `generatePath` constructs a URL with path parameters and query parameters.
 * @param {string} url - The `url` parameter is a string representing the base URL that you want to
 * append path parameters and query parameters to in order to generate a complete URL.
 * @param {GenericRecord} pathParams - Path parameters are dynamic values that are part of the URL
 * path. They are used to specify specific resources or endpoints in a RESTful API. For example, in a
 * URL like `/users/:userId`, `:userId` is a path parameter that can be replaced with an actual user ID
 * value when making
 * @param {GenericRecord} queryParams - The `queryParams` parameter in the `generatePath` function is a
 * GenericRecord object that contains key-value pairs representing the query parameters to be appended
 * to the URL. These query parameters are used to provide additional information to the server when
 * making a request.
 */
export const generatePath = (url: string, pathParams: GenericRecord, queryParams: GenericRecord): string =>
  !!url ? `${urlWithPathParams(url, pathParams)}${!!queryParams ? `?${generateQueryParams(queryParams)}` : ""}` : null;

/**
 * The function `generateKey` takes a path and an optional request body, and returns a string
 * concatenating the path and the JSON stringified request body.
 * @param {string} path - Path is a string representing the endpoint or resource path for an API
 * request.
 * @param {GenericRecord} [requestBody] - The `requestBody` parameter is an optional parameter of type
 * `GenericRecord`, which is used to store key-value pairs of data that can be sent in the request body
 * of an HTTP request. This parameter is used in the `generateKey` function to generate a unique key
 * based on the `path
 */
export const generateKey = (path: string, requestBody?: GenericRecord): string =>
  !!path ? `${path}${requestBody ? JSON.stringify(requestBody) : ""}` : null;
