import React, { useState, use<PERSON>emo } from "react";
import { isNil } from "lodash";
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
  TablePagination,
  IconButton,
  Collapse,
  Tooltip,
  SxProps,
  Stack,
} from "@mui/material";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import TableHeader from "./table-header/table-header";
import SearchAppBar from "@components/ui/search/enhanced-search";
import { uuid } from "@rubiconcarbon/shared-types";
import { basicComparator } from "@utils/comparators/comparator";
import { CsvBuilder } from "filefy";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { MISSING_DATA } from "@constants/constants";
import ActionButton from "../action-button/action-button-enhanced";
import Decimal from "decimal.js";
import { <PERSON><PERSON>R<PERSON><PERSON>, <PERSON>, MaybeNothing } from "@rubiconcarbon/frontend-shared";
import usePerformantEffect from "@/hooks/use-performant-effect";

export enum SortOrder {
  ASC = "asc",
  DESC = "desc",
}

export interface DefaultSort {
  columnName: string;
  order: SortOrder;
}

enum FormatterType {
  Display = "formatter",
  Export = "exportFormatter",
}

interface IDisplayFormatter {
  func: (
    input: MaybeNothing<Decimal | string | number | Date | boolean | Map<string, string | number | Date | boolean>>,
    secondaryArg?: any,
  ) => MaybeNothing<string | number | Date | JSX.Element>;
  inputFields?: (string | number | Date)[];
  overrideMissingDataDisplay?: boolean;
}

interface IExportFormatter {
  func: (
    input: MaybeNothing<Decimal | string | number | Date | boolean | Map<string, string | number | Date | boolean>>,
  ) => MaybeNothing<string | number>;
  inputFields?: (string | number | Date)[];
}

export interface ColDef {
  columnName: string;
  displayName: string;
  displayComponent?: JSX.Element;
  sortable?: boolean;
  exportable?: boolean;
  hide?: boolean;
  comparator?: (a: string | number | Date, b: string | number | Date, sortOrder: SortOrder) => number;
  formatter?: IDisplayFormatter;
  exportFormatter?: IExportFormatter;
  style?: SxProps;
  headerStyle?: SxProps;
}

interface EnhancedTableRow extends GenericRecord {
  id: uuid | number | string;
}

function getFormatterInputFields<T extends EnhancedTableRow>(
  inputFields: string[],
  row: T,
  colDef: ColDef,
): Map<string, string | number | Date> | string {
  if (!inputFields) return row[colDef.columnName];

  const map = new Map<string, string | number | Date>();
  inputFields.forEach((element) => {
    map.set(element, row[element]);
  });

  return map;
}

function formatRowValue<T extends EnhancedTableRow>(row: T, colDef: ColDef, formatterKey: string): string {
  if (!!colDef.columnName && isNil(row[colDef.columnName]) && !(colDef[formatterKey as keyof ColDef] as IDisplayFormatter)?.overrideMissingDataDisplay) {
    return MISSING_DATA;
  }

  if (colDef[formatterKey as keyof ColDef]) {
    const formatterInputFields = getFormatterInputFields((colDef[formatterKey as keyof ColDef] as IDisplayFormatter).inputFields as string[], row, colDef);
    return (colDef[formatterKey as keyof ColDef] as IDisplayFormatter).func(formatterInputFields) as string;
  }

  return row[colDef.columnName].toString();
}

interface RowProps<T> {
  row: T;
  columnsDef: ColDef[];
  expandedContent?: (row: T) => JSX.Element;
}

function Row<T extends EnhancedTableRow>(props: RowProps<T>): JSX.Element {
  const { row, columnsDef, expandedContent } = props;
  const [open, setOpen] = useState(false);
  return (
    <React.Fragment>
      <TableRow>
        {expandedContent && (
          <TableCell sx={{ paddingRight: 0 }}>
            <IconButton aria-label="expand row" size="small" onClick={() => setOpen(!open)}>
              {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
          </TableCell>
        )}

        {columnsDef.map((colDef, idx) => (
          <Maybe condition={!colDef.hide} key={colDef.columnName + idx}>
            <TableCell align="left" sx={colDef.style ?? {}}>
              {formatRowValue(row, colDef, FormatterType.Display)}
            </TableCell>
          </Maybe>
        ))}
      </TableRow>
      {expandedContent && (
        <TableRow>
          <TableCell
            style={{
              paddingBottom: 0,
              paddingTop: 0,
              backgroundColor: "rgb(247, 245, 245)",
              borderBottom: open ? "1px solid rgba(224, 224, 224, 1)" : "0",
            }}
            colSpan={columnsDef.length + 1}
          >
            <Collapse in={open} timeout="auto" unmountOnExit>
              <Box sx={{ margin: 1 }}>{expandedContent(row)}</Box>
            </Collapse>
          </TableCell>
        </TableRow>
      )}
    </React.Fragment>
  );
}

interface EnhancedTableProps<T> {
  columnsDef: ColDef[];
  defaultSort?: DefaultSort;
  data: T[];
  name?: string;
  exportable?: boolean;
  elevation?: number;
  rowsCountPerPage?: number;
  filter?: string;
  loading?: boolean;
  serverSidePagination?: boolean;
  serverSideSorting?: boolean;
  isStickyHeader?: boolean;
  maxHeight?: number;
  expandedContent?: (row: T) => JSX.Element;
  getFilteredData?: (input: string) => void;
  searchBarContent?: () => JSX.Element;
  onSearchHandler?: (input: string) => void;
  onPageChange?: (page: number, rowsPerPage: number) => void;
  onSortChange?: (columnName: string, order: SortOrder) => void;
  onExport?: () => Promise<T[]>;
}

export default function EnhancedTable<T extends EnhancedTableRow>(props: EnhancedTableProps<T>): JSX.Element {
  const {
    columnsDef,
    defaultSort,
    data,
    name = "table_info",
    rowsCountPerPage = 10,
    elevation = 1,
    filter = "",
    isStickyHeader = false,
    maxHeight = null,
    expandedContent,
    getFilteredData,
    searchBarContent,
    onSearchHandler,
    exportable,
    onPageChange,
    onSortChange,
    onExport,
    loading = false,
    serverSidePagination = false,
    serverSideSorting = false,
  } = props;
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(rowsCountPerPage);
  const [orderDirection, setOrderDirection] = useState<SortOrder>(defaultSort?.order ?? SortOrder.DESC);
  const [valueToOrderBy, setValueToOrderBy] = useState<string>(defaultSort?.columnName ?? "");
  const [searchFilter, setSearchFilter] = useState<string>(filter);
  const [localData, setLocalData] = useState<T[]>(data);
  const [isExporting, setIsExporting] = useState(false);

  usePerformantEffect(() => {
    setLocalData(data);
  }, [data]);

  usePerformantEffect(() => {
    if (getFilteredData) {
      setSearchFilter(filter);
      getFilteredData(filter);
    }
  }, [filter]);

  const handleChangePage = (event: unknown, newPage: number): void => {
    setPage(newPage);
    if (serverSidePagination && onPageChange) {
      onPageChange(newPage, rowsPerPage);
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const newRowsPerPage = +event.target.value;
    setRowsPerPage(newRowsPerPage);
    setPage(0);
    if (serverSidePagination && onPageChange) {
      onPageChange(0, newRowsPerPage);
    }
  };

  function onSortHandler(columnName: string): void {
    const isAscending = valueToOrderBy === columnName && orderDirection === SortOrder.ASC;
    const newOrderDirection = isAscending ? SortOrder.DESC : SortOrder.ASC;

    setValueToOrderBy(columnName);
    setOrderDirection(newOrderDirection);
    setPage(0);

    if (serverSideSorting && onSortChange) {
      onSortChange(columnName, newOrderDirection);
    }

    if ((serverSidePagination || serverSideSorting) && onPageChange) {
      onPageChange(0, rowsPerPage);
    }
  }

  const paginatedData = useMemo(() => {
    const sortData = (data: T[]): T[] => {
      if (serverSideSorting) {
        return data;
      }

      if (data) {
        const columnDef = columnsDef.find((element) => element.columnName === valueToOrderBy);

        return [...data].sort((a, b) => {
          if (isNil(a[valueToOrderBy])) return 0;
          const comparator = columnDef?.comparator ?? basicComparator;
          return comparator(a[valueToOrderBy], b[valueToOrderBy], orderDirection);
        });
      }
      return [];
    };
    if (serverSidePagination) {
      return localData;
    }
    return sortData(localData).slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  }, [
    localData,
    page,
    rowsPerPage,
    serverSidePagination,
    columnsDef,
    orderDirection,
    serverSideSorting,
    valueToOrderBy,
  ]);

  const onSearchChangeHandler = (value: string): void => {
    setPage(0);
    setSearchFilter(value);
    if (getFilteredData) {
      getFilteredData(value);
    }
    if (onSearchHandler) {
      onSearchHandler(value);
    }
  };

  const isExportable = (colDef: ColDef): boolean => {
    if ("exportable" in colDef && colDef.exportable === false) return false;

    return true;
  };

  const exportHandler = async (): Promise<void> => {
    setIsExporting(true);
    try {
      const exportableColDef = columnsDef.filter((colDef) => isExportable(colDef));
      const columns = exportableColDef?.map((colDef) => colDef.displayName);

      let exportData: T[];
      if (onExport) {
        exportData = await onExport();
      } else {
        exportData = data;
      }

      const rows = exportData.map((row) =>
        exportableColDef.map((colDef) => formatRowValue(row, colDef, FormatterType.Export)),
      );
      const csvBuilder = new CsvBuilder(`export_${name}.csv`);
      csvBuilder.setColumns(columns).addRows(rows).exportFile();
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Paper elevation={elevation}>
      {getFilteredData && (
        <SearchAppBar onChangeHandler={onSearchChangeHandler} filter={searchFilter}>
          <Stack direction="row" justifyContent="flex-end" gap={"var(--action-button-gap)"}>
            {searchBarContent && searchBarContent()}
            {exportable && (
              <Tooltip title="Export all data to csv file">
                <Box>
                  <ActionButton
                    onClickHandler={exportHandler}
                    startIcon={<FileDownloadIcon />}
                    tooltip="Export all data to csv file"
                    isDisabled={isExporting}
                    style={{
                      borderColor: "rgba(0, 0, 0, 0.23)",
                      color: "rgba(0, 0, 0, 0.54)",
                      backgroundColor: "rgba(255, 255, 255, 1)",
                      fontWeight: 600,
                      "&:hover": {
                        backgroundColor: "rgba(255, 255, 255, 1)",
                        boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
                      },
                    }}
                  >
                    {isExporting ? "Exporting..." : "Export"}
                  </ActionButton>
                </Box>
              </Tooltip>
            )}
          </Stack>
        </SearchAppBar>
      )}
      <TableContainer sx={{ maxHeight: maxHeight }}>
        <Table stickyHeader={isStickyHeader} aria-label="collapsible table">
          <TableHeader
            isExpandable={!isNil(expandedContent)}
            columnsDef={columnsDef}
            orderDirection={orderDirection}
            valueToOrderBy={valueToOrderBy}
            sortHandler={onSortHandler}
          />
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columnsDef.length + (expandedContent ? 1 : 0)}>Loading...</TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row) => (
                <Row key={row.id} row={row} columnsDef={columnsDef} expandedContent={expandedContent} />
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 100]}
        component="div"
        count={
          serverSidePagination ? (data?.length < rowsPerPage ? data?.length : -1) : localData ? localData?.length : -1
        }
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
}
