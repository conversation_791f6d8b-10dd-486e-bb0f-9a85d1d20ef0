import AddNewVintageForm from "@components/project-vintages/components/add-new-vintage-form";
import GenericDialog from "@components/ui/generic-dialog/generic-dialog";
import GenericTable from "@components/ui/generic-table";
import useGenericTableUtility from "@components/ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableColumnValueOption } from "@components/ui/generic-table/types/generic-table-column-value-options";
import { GenericTableRowModel } from "@components/ui/generic-table/types/generic-table-row-model";
import COLORS from "@components/ui/theme/colors";
import { COMPLIANCE_PERMIT, SERVER_PAGINATION_LIMIT } from "@constants/constants";
import { PrimitiveTypeBook } from "@models/primitive-type-book";
import useAuth from "@providers/auth-provider";
import { AxiosContext } from "@providers/axios-provider";
import useAutoCompleteOptions from "@hooks/use-auto-complete-options";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { AddCircleRounded, CloseRounded, ErrorOutlineRounded } from "@mui/icons-material";
import { Stack, Typography, Tooltip, Chip, Grid, Fab, Zoom } from "@mui/material";
import {
  MaybeNothing,
  currencyFormat,
  calculator,
  isNothing,
  pickFromRecord,
  toDecimal,
  px,
  omitFromRecord,
  Undefinable,
} from "@rubiconcarbon/frontend-shared";
import {
  uuid,
  ForwardQueryResponse,
  ForwardResponse,
  AdminProjectVintageResponse,
  TrimmedProjectVintageResponse,
  PermissionEnum,
  BookType,
  ForwardLineItemCreateRequest,
  ForwardStatus,
  ForwardLineItemUpdateRequest,
  ForwardLineItemSettleRequest,
  BookAction,
  AdminBookResponse,
  AllocationResponse,
  AssetTypeGroupedAllocationResponse,
  AssetType,
} from "@rubiconcarbon/shared-types";
import { AxiosResponse } from "axios";
import { useContext, useMemo, useRef, useState, useCallback, useEffect } from "react";
import { useToggle } from "react-use";
import { LINE_ITEM_COLUMNS } from "../constants/line-item-columns";
import { ForwardLineItem, MultiForwardLineItemFormModel } from "../models/forward";
import { HandleLineItemSubmit, ForwardLineItemExtensions } from "../types/forward-line-item";
import DeliverForm from "./deliver-form";
import { useLogger } from "@providers/logging";
import { ProjectVintageEntry } from "../types/expanded-row-types";
import { classValidatorResolver } from "@hookform/resolvers/class-validator";
import { GenericTableNoDataContent } from "@components/ui/generic-table/components/generic-table-row";
import { bookIsBreached } from "@utils/helpers/portfolio/book-is-breached";
import { GenericTableSimpleType } from "@components/ui/generic-table/types/generic-table-simple-type";
import usePerformantEffect from "@/hooks/use-performant-effect";
import useMultiAutoCompleteOptions from "@/hooks/use-multi-auto-complete-options";

import classes from "../styles/line-items.module.scss";
import dialogClasses from "../styles/dialog.module.scss";

type LineItemsProps = {
  hasParent: boolean;
  forwardId?: uuid;
  items: ForwardLineItem[];
  isBuy: boolean;
  projectId: uuid;
  projectVintages: ProjectVintageEntry;
  vintageAllocations: AllocationResponse[];
  books: AdminBookResponse[];
  exactLineItemKeyQuery?: string;

  toggleAmendingNestedRows: (nextValue?: boolean) => void;
  refreshForwards: () => Promise<ForwardQueryResponse>;
  onProjectVintageAddition: (projectId: uuid, vintage: AdminProjectVintageResponse) => void;
};

const MultiForwarLineItemFormResolver = classValidatorResolver(MultiForwardLineItemFormModel);

const LineItems = ({
  hasParent,
  forwardId,
  items,
  isBuy,
  projectId,
  projectVintages,
  vintageAllocations,
  books,
  exactLineItemKeyQuery,
  toggleAmendingNestedRows,
  refreshForwards,
  onProjectVintageAddition,
}: LineItemsProps): JSX.Element => {
  const { user } = useAuth();
  const { logger } = useLogger();
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const permissions = useMemo(() => user?.permissions || [], [user?.permissions]);
  const boundSubmitSettleLineItemCreator = useRef<() => HandleLineItemSubmit>(null);

  const hasPermissionToCreate = permissions.includes(PermissionEnum.FORWARDS_CREATE_LINE_ITEMS);

  const [lastAmendedIndex, setAmendedIndex] = useState<MaybeNothing<number>>();
  const [changeStatusRow, setChangeStatusRow] = useState<MaybeNothing<ForwardLineItem>>();
  const [bookBreachedRow, setBookBreachedRow] = useState<MaybeNothing<GenericTableRowModel<ForwardLineItem>>>();
  const [canDeliver, setCanDeliver] = useToggle(false);
  const [canSumbitNewVintage, setCanSumbitNewVintage] = useToggle(false);
  const [submittingDialogContent, setSubmittingDialogContent] = useToggle(false);
  const [openAddVintage, setOpenAddVintage] = useToggle(false);
  const [openBookBreached, setOpenBookBreached] = useToggle(false);
  const [openCancelLineItem, setOpenCancelLineItem] = useToggle(false);
  const [openDeliveryConfirmation, setOpenDeliveryConfirmation] = useToggle(false);

  const bindOnSubmitLineItem = (handleLineItemSubmit: () => HandleLineItemSubmit): void => {
    (boundSubmitSettleLineItemCreator as { current: () => HandleLineItemSubmit }).current = handleLineItemSubmit;
  };
  const handleOnSubmitSettleLineItem = (): Undefinable<HandleLineItemSubmit> => {
    if (boundSubmitSettleLineItemCreator.current) return boundSubmitSettleLineItemCreator.current();
  };

  const { table, form, untouchedFormValue, currentFormValue } = useGenericTableUtility<ForwardLineItem>({
    form: {
      mode: "onSubmit",
      resolver: MultiForwarLineItemFormResolver as any,
      defaultValues: {
        amends: [],
      },
    },
  });

  const { getValues, smartSetValue, watch, trigger, handleSubmit } = form || {};

  const allocatedVintageToBookTypes = useMemo(
    () =>
      Object.entries(
        (vintageAllocations || []).reduce(
          (record, allocation) => ({
            ...record,
            [allocation?.detailedAsset?.id ?? '']: Array.from(
              new Set(
                [
                  ...(record?.[allocation?.detailedAsset?.id as uuid] || []),
                  allocation?.owner?.allowedActions?.some((action) =>
                    [BookAction.BUY, BookAction.SELL].includes(action),
                  )
                    ? allocation?.owner?.type
                    : null,
                ].filter((type) => !!type),
              ),
            ),
          }),
          {} as Record<uuid, BookType[]>,
        ),
      ).reduce(
        (record, [id, types]) => ({
          ...record,
          ...(types?.length
            ? {
                [id]: types,
              }
            : {}),
        }),
        {} as Record<uuid, BookType[]>,
      ),
    [vintageAllocations],
  );

  const bookToVintages = useMemo(
    () =>
      (vintageAllocations || []).reduce(
        (record, allocation) => ({
          ...record,
          [allocation.owner.id]: {
            ...(record?.[allocation.owner.id] || {}),
            [allocation?.detailedAsset?.id || '']: omitFromRecord(allocation, ["owner"]),
          },
        }),
        {} as Record<uuid, Record<uuid, Omit<AllocationResponse, "owner">>>,
      ),
    [vintageAllocations],
  );

  const forwardVintages = useMemo(
    () =>
      isBuy
        ? projectVintages?.data // AdminProjectVintageResponse[]
        : vintageAllocations
            ?.filter(({ owner }) => owner.allowedActions.includes(BookAction.SELL))
            .map(({ detailedAsset }) => detailedAsset), // TrimmedProjectVintageResponse[]
    [isBuy, projectVintages?.data, vintageAllocations],
  ) as (AdminProjectVintageResponse | TrimmedProjectVintageResponse)[];

  const inCreateMode = useMemo(
() => currentFormValue?.amends?.some((amend) => amend?.creating === true),
    [currentFormValue?.amends],
  );

  const inEditMode = useMemo(
    () => currentFormValue?.amends?.some((amend) => amend?.editing === true),
    [currentFormValue?.amends],
  );

  const bookOptionsByVintage = useMemo(
    () =>
      forwardVintages?.reduce(
        (options, vintage) => ({
          ...options,
          [vintage.id]: [
            isBuy
              ? vintage.project?.projectType.category !== COMPLIANCE_PERMIT
                ? books.filter(
                    (book) => ![BookType.COMPLIANCE_DEFAULT, BookType.REHABILITATION_DEFAULT].includes(book?.type),
                  )
                : [books.find((book) => book.type === BookType.COMPLIANCE_DEFAULT)]
              : (allocatedVintageToBookTypes?.[vintage.id] || [])?.reduce(
                  (accum: PrimitiveTypeBook[], type: BookType): PrimitiveTypeBook[] => {
                    const book = books.find((book) => book?.type === type);
                    return book ? [...accum, book as unknown as PrimitiveTypeBook] : accum;
                  },
                  [],
                ),
          ]?.flat(),
        }),
        {} as Record<uuid, PrimitiveTypeBook[]>,
      ) || {},
    [allocatedVintageToBookTypes, books, forwardVintages, isBuy],
  );

  const vintageOptions = useAutoCompleteOptions({
    data: projectVintages?.data || [],
    keys: ["id", "name"],
    label: (entry) => entry?.name,
    value: (entry) => entry?.id,
    postTransform: (options) => options.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const bookOptions = useMultiAutoCompleteOptions({
    record: bookOptionsByVintage,
    keys: ["id", "name", "ownerAllocationsByAssetType"],
    label: (entry) => entry.name,
    displayLabel: (entry) => {
      const allocsByAssetType: AssetTypeGroupedAllocationResponse[] = entry?.ownerAllocationsByAssetType || [];
      const { groupedPrices } =
        allocsByAssetType?.find(({ assetType }) => assetType === AssetType.REGISTRY_VINTAGE) || {};

      return (
        <Stack width="100%" direction="row" justifyContent="space-between" alignItems="center">
          <span>{entry.name}</span>
          <Typography variant="caption" color="GrayText">
            {currencyFormat(
              calculator(groupedPrices?.totalPriceAllocated)
                .add(groupedPrices?.totalPricePendingBuy)
                .subtract(groupedPrices?.totalPricePendingSell)
                .calculate()
                .toNumber(),
            )}
          </Typography>
        </Stack>
      );
    },
    value: (entry) => entry.id,
    postTransform: (options) => options.sort((a, b) => a?.label?.localeCompare(b?.label)),
  });

  const columns = useMemo(
    () =>
      LINE_ITEM_COLUMNS.map((column) => {
        if (column.field === "projectVintage.id") {
          column.valueOptions = vintageOptions;
          column.autoCompleteLoading = projectVintages?.fetching;
        } else if (column.field === "book.id") {
          column.valueOptions = (row): GenericTableColumnValueOption[] => bookOptions[row?.projectVintage?.id];
          column.disable = (row): boolean => !bookOptions?.[row?.projectVintage?.id]?.length;
          column.formHelperText = ({ row }): JSX.Element =>
            !!row?.projectVintage?.id && !bookOptions?.[row.projectVintage.id] ? (
              <>Insufficient inventory to fulfill the order</>
            ) : <></>;
        } else if (column.field === "rawPrice") {
          if (isBuy) {
            column.label = "Sub Total";
          } else {
            column.label = "Gross Revenue";
          }
        } else if (column.field === "grandTotal") {
          if (isBuy) {
            column.label = "Grand Total";
            column.deriveDataValue = (row): GenericTableSimpleType =>
              calculator(row?.rawPrice).add(row?.serviceFee).add(row?.otherFee).calculate().toString();
          } else {
            column.label = "Net Revenue";
            column.deriveDataValue = (row): GenericTableSimpleType =>
              calculator(row?.rawPrice).add(-(row?.serviceFee || 0)).add(-(row?.otherFee || 0)).calculate().toString();
          }
        }

        return column;
      }),
    [bookOptions, isBuy, projectVintages?.fetching, vintageOptions],
  );

  usePerformantEffect(() => {
    toggleAmendingNestedRows(inCreateMode || inEditMode);
  }, [inCreateMode, inEditMode]);

  useEffect(() => {
    const subscription = watch?.(({ amends = [] }, { name, type }) => {
      if (type === "change") {
        const [, index, ...path] = ((name as any) || "").split(".");
        const joinedPath = path.join(".") as any;

        if (!isNothing(index)) {
          const numIndex = Number(index);

          setAmendedIndex(numIndex);

          const formRow = amends?.at(numIndex);

          const amount = toDecimal(formRow?.expectedAmount, { treatNothingAsNaN: true });

          switch (name) {
            case `amends.${numIndex}.projectVintage.id`: {
              const id = formRow ? pickFromRecord(formRow as Record<string, unknown>, [joinedPath], true)?.[joinedPath] as uuid : undefined;

              if (id === "_add_vintage_") {
                setOpenAddVintage(true);
                smartSetValue?.(name as any, undefined);
              }
              break;
            }
            case `amends.${numIndex}.unitPrice`:
              if (!amount.isNaN() && !amount.isZero())
                smartSetValue?.(
                  `amends.${numIndex}.rawPrice` as any,
                  calculator(formRow?.unitPrice).multiply(formRow?.expectedAmount).calculate().toString(),
                  {
                    shouldValidate: true,
                  },
                );
              else smartSetValue?.(`amends.${numIndex}.rawPrice` as any, "0" as any, { shouldValidate: true });
              break;
            case `amends.${numIndex}.expectedAmount`:
              if (!amount.isNaN() && !amount.isZero())
                smartSetValue?.(
                  `amends.${numIndex}.rawPrice` as any,
                  calculator(formRow?.unitPrice).multiply(amount).calculate().toString() as any,
                  {
                    shouldValidate: true,
                  },
                );
              else smartSetValue?.(`amends.${numIndex}.rawPrice` as any, "0" as any, { shouldValidate: true });
              break;
            case `amends.${numIndex}.rawPrice`:
              if (!amount.isNaN() && !amount.isZero())
                smartSetValue?.(
                  `amends.${numIndex}.unitPrice` as any,
                  calculator(formRow?.rawPrice, { treatNothingAsNaN: true })
                    .divide(formRow?.expectedAmount)
                    .calculate()
                    .toString() as any,
                  {
                    shouldValidate: true,
                  },
                );
              break;
            default:
              break;
          }

          if (!!formRow?.projectVintage?.id && !!formRow?.book?.id && !toDecimal(formRow?.rawPrice).isNaN()) {
            const book = bookOptionsByVintage?.[formRow?.projectVintage?.id as uuid]?.find(
              ({ id }) => formRow?.book?.id == (id as uuid),
            );

            smartSetValue?.(`amends.${numIndex}.book.type` as any, book?.type as any);
            smartSetValue?.(
              `amends.${numIndex}.bookBreached` as any,
              book ? bookIsBreached(book, toDecimal(formRow?.rawPrice)) : false as any,
            );
          }
        }
      }
    });

    return (): void => subscription?.unsubscribe();
  }, [bookOptionsByVintage, currentFormValue, setOpenAddVintage, smartSetValue, watch]);

  const toRowModel = useCallback(
    (row: ForwardLineItem): GenericTableRowModel<ForwardLineItem> => {
      const unitPrice = calculator(row?.rawPrice)
        .divide(row?.status === ForwardStatus.SETTLED ? row?.settledAmount : row?.expectedAmount)
        .calculate()
        .toString();

      return {
        ...row,
        unitPrice,
        bookBreached: bookIsBreached(
          bookOptionsByVintage?.[row?.projectVintage?.id]?.find(({ id }) => row?.book?.id === id) as PrimitiveTypeBook,
          calculator(row?.unitPrice)
            .multiply(row?.status === ForwardStatus.SETTLED ? row?.settledAmount : row?.expectedAmount)
            .calculate(),
        ),
      };
    },
    [bookOptionsByVintage],
  );

  const createLineItem = async (row: GenericTableRowModel<ForwardLineItem>): Promise<void> => {
    try {
      const payload: ForwardLineItemCreateRequest = {
        bookId: row?.book?.id,
        expectedAmount: Number(row.expectedAmount),
        maxAmount: Number(row.maxAmount),
        minAmount: Number(row.minAmount),
        originalDeliveryDate: new Date(row.originalDeliveryDate.toString()),
        rawPrice: toDecimal(row.rawPrice),
        projectVintageId: row.projectVintage.id,
        serviceFee: toDecimal(row?.serviceFee),
        otherFee: toDecimal(row?.otherFee),
      };

      await api.post<ForwardLineItemCreateRequest, AxiosResponse<ForwardResponse>>(
        `admin/forwards/${forwardId}/line-items`,
        payload,
      );

      enqueueSuccess("Successfully created line item");

      table?.handleCancelRowAmendment(row);

      await refreshForwards();
    } catch (error: any) {
      enqueueError(`Unable to save line item: ${error?.response?.data?.message?.join(". ")}`);
      logger.error(`Unable to save line item: ${error?.message}.`, {});
    }
  };

  const updateLineItem = async (row: GenericTableRowModel<ForwardLineItem>): Promise<void> => {
    try {
      const payload: ForwardLineItemUpdateRequest = {
        expectedAmount: Number(row.expectedAmount),
        maxAmount: Number(row.maxAmount),
        minAmount: Number(row.minAmount),
        lastUpdatedDeliveryDate: new Date(row.lastUpdatedDeliveryDate.toString()),
        rawPrice: toDecimal(row.rawPrice),
        bookId: row?.book?.id,
        serviceFee: toDecimal(row?.serviceFee),
        otherFee: toDecimal(row?.otherFee),
      };

      await api.patch<ForwardLineItemCreateRequest, AxiosResponse<ForwardResponse>>(
        `admin/forwards/${forwardId}/line-items/${row?.id}`,
        payload,
      );

      enqueueSuccess("Successfully updated line item");

      table?.handleCancelRowAmendment(row);

      await refreshForwards();
    } catch (error: any) {
      enqueueError(`Unable to update line item: ${error?.response?.data?.message?.join(". ")}`);
      logger.error(`Unable to update line item: ${error?.message}.`, {});
    }
  };

  const updateLineItemStatus = async (
    row: GenericTableRowModel<ForwardLineItem>,
    status: "settle" | "cancel",
  ): Promise<void> => {
    setChangeStatusRow(row);

    if (status === "cancel") setOpenCancelLineItem(true);
    else setOpenDeliveryConfirmation(true);
  };

  const cancelLineItem = async (row: ForwardLineItem): Promise<void> => {
    try {
      await api.patch<ForwardLineItemUpdateRequest, AxiosResponse<ForwardResponse>>(
        `admin/forwards/${forwardId}/line-items/${row?.id}/cancel`,
      );

      enqueueSuccess("Successfully canceled line item");

      await refreshForwards();
      setOpenCancelLineItem(false);
    } catch (error: any) {
      enqueueError(`Unable to cancel line item.`);
      logger.error(`Unable to cancel line item: ${error?.message}.`, {});
    }
  };

  const settleLineItem = async (formData: ForwardLineItem): Promise<void> => {
    try {
      const payload: ForwardLineItemSettleRequest = {
        ...px(
          {
            bookId: formData?.book?.id || null,
            expectedAmount: formData?.expectedAmount || null,
            lastUpdatedDeliveryDate: formData?.lastUpdatedDeliveryDate?.toString() || null,
            minAmount: formData?.minAmount || null,
            maxAmount: formData?.maxAmount || null,
            serviceFee: formData?.serviceFee || null,
            otherFee: formData?.otherFee || null,
            price: formData?.rawPrice || null,
          },
          [null],
        ),
        settledAmount: Number(formData?.settledAmount),
        settledAt: new Date((formData.settledAt as any).toString()),
      };

      await api.patch<ForwardLineItemSettleRequest, AxiosResponse<ForwardResponse>>(
        `admin/forwards/${forwardId}/line-items/${formData?.id}/settle`,
        payload,
      );

      enqueueSuccess("Successfully settled line item");

      await refreshForwards();
      setOpenDeliveryConfirmation(false);
    } catch (error: any) {
      enqueueError(`Unable to settle line item.`);
      logger.error(`Unable to settle line item: ${error?.message}.`, {});
    }
  };

  const commitLineItem = async (row: GenericTableRowModel<ForwardLineItem>): Promise<void> => {
    const index = getValues?.()?.amends?.findIndex(({ id }) => id === row?.id);

    if (index !== undefined) {
      const canSubmit = await trigger?.(`amends.${index}`);

      if (canSubmit) {
        if (row?.bookBreached) {
          const priceChanged = !toDecimal(untouchedFormValue?.amends?.[index]?.unitPrice).equals(
            toDecimal(currentFormValue?.amends?.at(index)?.unitPrice),
          );
          const expectedAmountChanged =
            Number(untouchedFormValue?.amends?.[index]?.expectedAmount) !==
            Number(currentFormValue?.amends?.[index]?.expectedAmount);
  
          if (priceChanged || expectedAmountChanged) {
            setBookBreachedRow(row);
            setOpenBookBreached(true);
            return;
          }
        }
  
        if (row?.creating) await createLineItem(row);
        else await updateLineItem(row);
      }
    }
  };

  const onSubmit = async (): Promise<void> => {};

  return (
    <Stack className={classes.LineItems} alignItems="flex-end" gap={1}>
      <GenericTable
        id="line-items"
        columns={columns}
        pageableData={{
          data: items,
          page: {
            offset: 0,
            limit: SERVER_PAGINATION_LIMIT,
            size: items?.length,
            totalCount: items?.length,
          },
        }}
        styles={{
          headerRow: {
            backgroundColor: "#EEEEEE",
            zIndex: 500,
          },
          bodyRow: (row: GenericTableRowModel<ForwardLineItem>) => ({
            backgroundColor:
              !!exactLineItemKeyQuery && row?.uiKey === exactLineItemKeyQuery ? COLORS.rubiconGreenPale : "#FFFFFF",
          }),
        }}
        toRowModel={toRowModel}
        isMultiEditable
        appendNewRows
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        bindCancelRowAmendment={table?.bindCancelRowAmendment}
        extensions={
          {
            readonlyParent: true,
            settleLineItem: (row) => updateLineItemStatus(row, "settle"),
            cancelLineItem: (row) => updateLineItemStatus(row, "cancel"),
          } as ForwardLineItemExtensions
        }
        onFormSubmit={handleSubmit?.(onSubmit)}
        onRowSubmit={commitLineItem}
        renderNoDataContent={
          !hasParent ? (
            <GenericTableNoDataContent>
              <Grid className={classes.NoData} container>
                <Grid item>
                  <Chip
                    className={classes.Chip}
                    icon={<ErrorOutlineRounded className={classes.InfoIcon} />}
                    label={<Typography className={classes.Text}>Please create a forward order first</Typography>}
                  />
                </Grid>
              </Grid>
            </GenericTableNoDataContent>
          ) : !items?.length ? (
            <GenericTableNoDataContent>
              <Grid className={classes.NoData} container>
                <Grid item>
                  <Typography variant="body1" textAlign="center">
                    No Data Found.
                  </Typography>
                  <Typography textAlign="center">
                    Please click{" "}
                    <Typography component="span" variant="h6" fontWeight="bold">
                      {" "}
                      +{" "}
                    </Typography>{" "}
                    icon to add line items.
                  </Typography>
                </Grid>
              </Grid>
            </GenericTableNoDataContent>
          ) : null
        }
      />
      <Zoom in={hasParent} unmountOnExit>
        <Tooltip title={!hasPermissionToCreate ? "Insufficient permissions" : ""}>
          <Fab
            className={classes.AddLineItem}
            size="medium"
            onClick={table?.handleAddRow}
            disabled={!hasPermissionToCreate || !hasParent || inEditMode}
            sx={{ zIndex: 500 }}
          >
            <AddCircleRounded
              className={classes.AddIcon}
              htmlColor={hasPermissionToCreate && hasParent && !inEditMode ? COLORS.rubiconGreen : "darkgray"}
            />
          </Fab>
        </Tooltip>
      </Zoom>
      <GenericDialog
        open={openAddVintage}
        title="Add New Vintage"
        onClose={() => setOpenAddVintage(false)}
        positiveAction={{
          buttonText: "CREATE",
          disabled: !canSumbitNewVintage,
          onClick: () => setSubmittingDialogContent(true),
        }}
        negativeAction
        onNegativeClick={() => setOpenAddVintage(false)}
        classes={{
          root: dialogClasses.AddOption,
          content: dialogClasses.AddOptionContent,
          actions: dialogClasses.Actions,
        }}
      >
        <AddNewVintageForm
          projectId={projectId}
          submitting={submittingDialogContent}
          setCanSumbit={setCanSumbitNewVintage}
          setSubmitting={setSubmittingDialogContent}
          onSubmitSuccess={(projectVintage) => {
            setTimeout(() => {
              setOpenAddVintage(false);

              if (projectVintage?.project?.id) {
                onProjectVintageAddition(projectVintage.project.id, projectVintage);
              }

              smartSetValue?.(`amends.${lastAmendedIndex}.projectVintage.id` as any, projectVintage?.id, {
                shouldValidate: !!projectId,
              });
            });
          }}
        />
      </GenericDialog>
      <GenericDialog
        open={openBookBreached}
        title="Exceeding Portfolio Limits"
        onClose={() => setOpenBookBreached(false)}
        positiveAction={{
          buttonText: "YES, CONTINUE",
          className: dialogClasses.DestructivePositiveAction,
          onClick: async () => {
            setOpenBookBreached(false);

            if (bookBreachedRow?.creating) await createLineItem(bookBreachedRow);
            else if (bookBreachedRow) await updateLineItem(bookBreachedRow);
          },
        }}
        negativeAction={{
          buttonText: "CANCEL",
          onClick: () => setOpenBookBreached(false),
        }}
        classes={{
          title: `${dialogClasses.Title} ${dialogClasses.DestructiveTitle}`,
          actions: dialogClasses.Actions,
        }}
      >
        <Stack component="p" rowGap={1}>
          <Typography component="span">
            This line item will <strong>exceed</strong> the portfolio limits. By proceeding, the{" "}
            {bookBreachedRow?.book?.name} book will surpass its approved dollar amount.
          </Typography>
          <Typography component="span">Do you still wish to continue?</Typography>
        </Stack>
      </GenericDialog>
      <GenericDialog
        open={openCancelLineItem}
        title="Cancel Line Item"
        onClose={() => setOpenCancelLineItem(false)}
        positiveAction={{
          buttonText: "YES, CANCEL",
          onClick: () => changeStatusRow && cancelLineItem(changeStatusRow),
          className: dialogClasses.DestructivePositiveAction,
        }}
        negativeAction={{
          buttonText: "NO",
          onClick: () => setOpenCancelLineItem(false),
        }}
        classes={{
          root: dialogClasses.Dialog,
          title: dialogClasses.DestructiveTitle,
          content: dialogClasses.Content,
          actions: dialogClasses.Actions,
        }}
      >
        <Typography>Are you sure you want to cancel the line item?</Typography>
      </GenericDialog>
      <GenericDialog
        open={openDeliveryConfirmation}
        dismissIcon={<CloseRounded />}
        title="Delivery Confirmation"
        onClose={() => {
          setChangeStatusRow(undefined);
          setOpenDeliveryConfirmation(false);
        }}
        positiveAction={{
          buttonText: "SAVE",
          disabled: !canDeliver,
          onClick: handleOnSubmitSettleLineItem,
        }}
        negativeAction
        onNegativeClick={() => {
          setChangeStatusRow(undefined);
          setOpenDeliveryConfirmation(false);
        }}
        classes={{
          root: dialogClasses.Delivery,
          content: dialogClasses.DeliveryContent,
          actions: dialogClasses.Actions,
        }}
      >
        <DeliverForm
          isBuy={isBuy}
          loadingAllocations={false}
          row={{
            ...changeStatusRow,
            rawPrice: "", // removed rawPrice since it needs to be set with settledAmount
            stagedForDelivery: true,
          } as ForwardLineItem}
          books={changeStatusRow?.projectVintage?.id ? bookOptionsByVintage[changeStatusRow.projectVintage.id] : []}
          bookOptions={changeStatusRow?.projectVintage?.id ? bookOptions[changeStatusRow.projectVintage.id] : []}
          bookToVintages={bookToVintages}
          bindOnSubmitLineItem={bindOnSubmitLineItem}
          onFormValidationChange={setCanDeliver}
          settleLineItem={settleLineItem}
        />
      </GenericDialog>
    </Stack>
  );
};

export default LineItems;
