import { Box, LinearProgress, Stack, useMediaQuery } from "@mui/material";
import React, { useEffect, useState, useCallback, useContext } from "react";
import {
  PricingEstimateRequest,
  PricingEstimateResponse,
  AdminBookResponse,
  AllocationResponse,
  GroupedAllocationWithNestedResponse,
} from "@rubiconcarbon/shared-types";
import currencyFormat from "@/utils/formatters/currency-format";
import { MISSING_DATA } from "@constants/constants";
import Decimal from "decimal.js";
import { AxiosContext } from "@providers/axios-provider";
import BasketSalePrice from "./basket-sale-price";
import { KeyedMutator } from "swr";
import { Maybe, Nullable } from "@rubiconcarbon/frontend-shared";
import SummaryGroup, { itemGroup } from "@components/ui/summary-group/item-group";
import PortfolioHistPrice from "./portfolio-historical-price";

const cellBorder = {
  borderRight: "solid",
  borderRightWidth: "2px",
  borderRightColor: "rgba(0, 0, 0, 0.12)",
  marginRight: "0px",
};

interface BasketSummaryProps {
  book: AdminBookResponse;
  refresh: KeyedMutator<AdminBookResponse>;
}

interface ComputedPrice {
  unitPrice?: Decimal;
  unitCost?: Decimal;
  total?: Decimal;
  quantity?: number;
}

const buildAlgoPayload = (
  bookVintages: AllocationResponse[],
  withRisk: boolean = false,
): Nullable<PricingEstimateRequest> => {
  if (!bookVintages || bookVintages.length < 1) return null;

  return bookVintages
    .filter((c) => c.amountAllocated > 0)
    .map((c) => ({
      vintageId: c?.asset.id,
      quantity: c.amountAllocated,
      buffer: withRisk ? undefined : 0,
    }));
};

export default function BasketSummaryTotals(props: BasketSummaryProps): JSX.Element {
  const { book, refresh } = props;
  const [computedPrices, setComputedPrices] = useState<ComputedPrice | undefined>(undefined);
  const [computedPricesWithRisk, setComputedPricesWithRisk] = useState<ComputedPrice | undefined>(undefined);
  const isSmallerScreen = useMediaQuery("(max-width: 1500px)");
  const { api } = useContext(AxiosContext);

  const getAlgorithmPrice = useCallback(
    (totalQuantity: number, payload: Nullable<PricingEstimateRequest>, withRisk: boolean) => {
      if (payload && payload.length > 0) {
        const setFn = withRisk ? setComputedPricesWithRisk : setComputedPrices;
        api
          .post<PricingEstimateResponse>("admin/pricing/estimate", payload)
          .then((data) => {
            if (!!data?.data?.priceEstimate && !!data?.data?.unitPrice) {
              const unitPrice =
                totalQuantity && !!data.data.priceEstimate
                  ? new Decimal(data.data.priceEstimate).dividedBy(totalQuantity)
                  : undefined;
              const unitCost =
                totalQuantity && !!data.data.costEstimate
                  ? new Decimal(data.data.costEstimate).dividedBy(totalQuantity)
                  : undefined;
              const total = data.data.priceEstimate;
              setFn({ unitPrice, unitCost, total, quantity: totalQuantity });
            } else {
              setFn(undefined);
            }
          })
          .catch((e) => {
            console.error(e);
            setFn(undefined);
          });
      }
    },
    [api, setComputedPrices, setComputedPricesWithRisk],
  );

  useEffect(() => {
    const detailedAllocations = book?.ownerAllocations as GroupedAllocationWithNestedResponse;
    const totalPortfolioVolume = detailedAllocations.totalAmountAllocated;
    let payload = buildAlgoPayload(detailedAllocations.allocations, false);

    if (payload == null) {
      setComputedPrices(undefined);
      setComputedPricesWithRisk(undefined);
    } else {
      getAlgorithmPrice(totalPortfolioVolume, payload, false);

      payload = buildAlgoPayload(detailedAllocations.allocations, true);
      getAlgorithmPrice(totalPortfolioVolume, payload, true);
    }
  }, [book, getAlgorithmPrice]);

  const customerPortalPriceGroup: itemGroup = {
    groupTitle: "Customer Portal Price",
    actions: (
      <Stack direction="row" gap={1}>
        <BasketSalePrice
          book={book}
          refresh={refresh}
          algoPrice={computedPrices?.unitPrice}
          algoPriceWithRisk={computedPricesWithRisk?.unitPrice}
        />
        <PortfolioHistPrice bookId={book?.id} price={book?.purchasePrice} bookName={book?.name} />
      </Stack>
    ),
    items: [
      {
        label: "Price",
        value: <>{book?.purchasePrice ? currencyFormat(+book?.purchasePrice) : MISSING_DATA}</>,
      },
      {
        label: "Risk Adjustment",
        value: <>{book?.purchasePriceWithBuffer ? currencyFormat(+book?.purchasePriceWithBuffer) : MISSING_DATA}</>,
      },
    ],
  };

  const algorithmicPriceGroup: itemGroup = {
    groupTitle: "Algorithmic Price",
    items: [
      {
        label: "Price",
        value: (
          <div>
            <Maybe condition={computedPrices === undefined}>
              <div style={{ height: 30, paddingTop: 22 }}>
                <LinearProgress sx={{ maxWidth: 50 }} />
              </div>
            </Maybe>
            <Maybe condition={computedPrices !== undefined}>
              {computedPrices?.unitPrice ? currencyFormat(+computedPrices?.unitPrice) : MISSING_DATA}
            </Maybe>
          </div>
        ),
      },
      {
        label: "Risk Adjustment",
        value: (
          <div>
            <Maybe condition={computedPrices === undefined}>
              <div style={{ height: 30, paddingTop: 22 }}>
                <LinearProgress sx={{ maxWidth: 50 }} />
              </div>
            </Maybe>
            <Maybe condition={computedPrices !== undefined}>
              {computedPricesWithRisk?.unitPrice ? currencyFormat(+computedPricesWithRisk?.unitPrice) : MISSING_DATA}
            </Maybe>
          </div>
        ),
      },
    ],
  };

  const costBasisGroup: itemGroup = {
    groupTitle: "Cost Basis",
    items: [
      {
        label: "Without buffer",
        value: (
          <div>
            <Maybe condition={computedPrices === undefined}>
              <div style={{ height: 30, paddingTop: 22, width: "100%" }}>
                <LinearProgress sx={{ maxWidth: 50 }} />
              </div>
            </Maybe>
            <Maybe condition={computedPrices !== undefined}>
              {computedPrices?.unitCost ? currencyFormat(+computedPrices?.unitCost, 4) : MISSING_DATA}
            </Maybe>
          </div>
        ),
      },
      {
        label: "With buffer",
        value: (
          <div>
            <Maybe condition={computedPrices === undefined}>
              <div style={{ height: 30, paddingTop: 22 }}>
                <LinearProgress sx={{ maxWidth: 50 }} />
              </div>
            </Maybe>
            <Maybe condition={computedPrices !== undefined}>
              {computedPricesWithRisk?.unitCost ? currencyFormat(+computedPricesWithRisk?.unitCost, 4) : MISSING_DATA}
            </Maybe>
          </div>
        ),
      },
    ],
  };

  return (
    <Stack direction={isSmallerScreen ? "column" : "row"}>
      <Box mt={2} width="420px" sx={{ ...cellBorder, borderWidth: isSmallerScreen ? 0 : "2px" }}>
        <SummaryGroup group={customerPortalPriceGroup} />
      </Box>

      <Box mt={2} width="340px" sx={{ ...cellBorder, borderWidth: isSmallerScreen ? 0 : "2px" }}>
        <SummaryGroup group={algorithmicPriceGroup} />
      </Box>

      <Box mt={2} width="340px">
        <SummaryGroup group={costBasisGroup} />
      </Box>
    </Stack>
  );
}
