import { GenericTableFieldSizeEnum } from "@/components/ui/generic-table/constants/generic-table-field-size.enum";
import { MarketingAgreementModel } from "../models/marketing-agreement";
import { GenericTableColumn } from "@/components/ui/generic-table/types/generic-table-column";
import SuspendedChip from "@/components/ui/suspended-chip/suspended-chip";
import { Stack, Typography, Grid, capitalize, Chip, Tooltip, IconButton, Box, Divider, Badge } from "@mui/material";
import { classcat, Maybe } from "@rubiconcarbon/frontend-shared";
import { MarketingAgreementStatus, PermissionEnum } from "@rubiconcarbon/shared-types";
import { GenericTableRowModel } from "@/components/ui/generic-table/types/generic-table-row-model";
import { MouseEvent, useContext, useMemo } from "react";
import { AuthContext } from "@/providers/auth-provider";
import useGenericTableRowActions from "@/components/ui/generic-table/hooks/use-generic-table-row-actions";
import useGenericTableRowState from "@/components/ui/generic-table/hooks/use-generic-table-row-state";
import MatIcon from "@/components/ui/mat-icon/mat-icon";
import COLORS from "@/components/ui/theme/colors";
import { MarketingAgreementExtensions } from "../types/marketing-agreement";
import { GenericTableExternal } from "@/components/ui/generic-table/types/generic-table-external";
import { useAtomValue, useStore } from "jotai";
import GenericTableFormErrorHelperText from "@/components/ui/generic-table/components/generic-table-form-error-helper-text";
import { externalAtom } from "@/components/ui/generic-table/state";
import GenericTableDocumentsButton from "@/components/ui/generic-table/components/generic-row-document-button";
import Link from "next/link";
import dateFormatter from "@/utils/formatters/dateFormatter";

import renderedClasses from "../styles/rendered-cell.module.scss";

type RowOnlyProps = {
  row: GenericTableRowModel<MarketingAgreementModel>;
};

const MarketingFeeButton = ({ row }: RowOnlyProps): JSX.Element => {
  const store = useStore();
  const { extensions } = useAtomValue(externalAtom, { store }) as GenericTableExternal<
    MarketingAgreementModel,
    MarketingAgreementExtensions
  >;
  const { amending, isRowActive, errors } = useGenericTableRowState<MarketingAgreementModel>(row);

  const { amendingNestedRows, toggleMarketingFeesModal, setViewingRow } = useMemo(
    () => (typeof extensions === "function" ? extensions(row) : extensions),
    [extensions, row],
  );
  const hasError = useMemo(() => isRowActive && !!errors?.amends?.[0]?.feeStructure, [errors?.amends, isRowActive]);
  const disabled = useMemo(() => amending && !isRowActive, [amending, isRowActive]);

  const inActive = amendingNestedRows() || disabled;

  const openModal = (event: MouseEvent<HTMLButtonElement>): void => {
    event?.preventDefault();
    event?.stopPropagation();

    if (!isRowActive) setViewingRow(row);
    toggleMarketingFeesModal();
  };

  return (
    <IconButton className={renderedClasses.IconButton} disabled={inActive} onClick={openModal}>
      <Badge
        variant="dot"
        invisible={!row?.feeStructure?.length}
        classes={{ dot: classcat([renderedClasses.Badge, { [renderedClasses.Disabled]: inActive }]) }}
      >
        <MatIcon
          value="file_open_outlined"
          variant="outlined"
          size={24}
          color={hasError ? "error" : inActive ? "disabled" : "primary"}
        />
      </Badge>
    </IconButton>
  );
};

const ActionsCell = ({ row }: RowOnlyProps): JSX.Element => {
  const { user } = useContext(AuthContext);
  const store = useStore();
  const { extensions } = useAtomValue(externalAtom, { store }) as GenericTableExternal<
    MarketingAgreementModel,
    MarketingAgreementExtensions
  >;

  const { toggleEdit, cancelAmendment } = useGenericTableRowActions<MarketingAgreementModel>();
  const { creating, editing, dirty, submitting, disabled, isRowActive } =
    useGenericTableRowState<MarketingAgreementModel>(row);
  const { amendingNestedRows, setViewingRow, toggleCancelAgreementModal } = useMemo(
    () => (typeof extensions === "function" ? extensions(row) : extensions),
    [extensions, row],
  );

  const permissions = useMemo(() => user.permissions, [user.permissions]);

  const hasPermissionToEdit = permissions.includes(PermissionEnum.MARKETING_AGREEMENTS_UPDATE);
  const hasPermissionToCancel = permissions.includes(PermissionEnum.MARKETING_AGREEMENTS_CANCEL);

  return (
    <Stack className={renderedClasses.Actions} direction="row" alignItems="center" gap={1}>
      <Maybe condition={!isRowActive}>
        <Tooltip title={!hasPermissionToEdit ? "Insufficient permissions" : "Edit Marketing Agreement"}>
          <Box>
            <IconButton
              className={renderedClasses.IconButton}
              color="primary"
              disabled={
                row?.status === MarketingAgreementStatus.CANCELED ||
                amendingNestedRows() ||
                !hasPermissionToEdit ||
                disabled
              }
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();
                toggleEdit(row);
              }}
            >
              <MatIcon
                value="edit"
                variant="round"
                size={25}
                color={
                  row?.status === MarketingAgreementStatus.CANCELED ||
                  amendingNestedRows() ||
                  !hasPermissionToEdit ||
                  disabled
                    ? "disabled"
                    : "primary"
                }
              />
            </IconButton>
          </Box>
        </Tooltip>
        <Maybe condition={row?.status !== MarketingAgreementStatus.CANCELED}>
          <Divider sx={{ height: 28.5 }} orientation="vertical" />
          <Tooltip title={!hasPermissionToCancel ? "Insufficient permissions" : "Cancel Marketing Agreement"}>
            <Box>
              <IconButton
                className={renderedClasses.IconButton}
                color="primary"
                disabled={
                  row?.status === MarketingAgreementStatus.CANCELED ||
                  amendingNestedRows() ||
                  !hasPermissionToCancel ||
                  disabled
                }
                onClick={(event: MouseEvent<HTMLButtonElement>) => {
                  event?.preventDefault();
                  event?.stopPropagation();
                  setViewingRow(row);
                  toggleCancelAgreementModal();
                }}
              >
                <MatIcon
                  value="highlight_off"
                  variant="round"
                  size={25}
                  color={
                    row?.status === MarketingAgreementStatus.CANCELED ||
                    amendingNestedRows() ||
                    !hasPermissionToCancel ||
                    disabled
                      ? "disabled"
                      : "primary"
                  }
                />
              </IconButton>
            </Box>
          </Tooltip>
        </Maybe>
      </Maybe>
      <Maybe condition={isRowActive}>
        <Tooltip title={`${creating ? "Create" : "Update"} Marketing Agreement`}>
          <Box>
            <IconButton
              className={renderedClasses.IconButton}
              sx={{ color: COLORS.rubiconGreen }}
              onClick={(event: MouseEvent<HTMLButtonElement>) => event.stopPropagation()}
              type="submit"
              disabled={submitting || (editing && !dirty)}
            >
              <MatIcon value="check" variant="round" size={25} />
            </IconButton>
          </Box>
        </Tooltip>
        <Divider sx={{ height: 28.5 }} orientation="vertical" />
        <Tooltip title={"Cancel edit"}>
          <Box>
            <IconButton
              className={renderedClasses.IconButton}
              sx={{ color: COLORS.red }}
              onClick={(event: MouseEvent<HTMLButtonElement>) => {
                event?.preventDefault();
                event?.stopPropagation();

                if (creating) cancelAmendment();
                else toggleEdit(row);
              }}
            >
              <MatIcon value="cancel" variant="round" color="error" size={25} />
            </IconButton>
          </Box>
        </Tooltip>
      </Maybe>
    </Stack>
  );
};

export const MARKETING_AGREEMENT_COLUMNS: GenericTableColumn<MarketingAgreementModel>[] = [
  {
    field: "uiKey",
    label: "Transaction Key",
    creatable: false,
    editable: false,
    hide: (rows) => rows?.at(0)?.creating,
    width: GenericTableFieldSizeEnum.medium,
    fixedWidth: true,
  },
  {
    field: "project.id",
    label: "Project",
    type: "async-autocomplete",
    editable: false,
    placeholder: "Type the project ID or name",
    asyncAutocompleteOptions: {
      keys: ["id", "name", "registryProjectId"],
      request: {
        url: "admin/projects/search",
        queryParams: {
          name: true,
          id: true,
          fuzzy: true,
          limit: 50,
        },
      },
      label: (entry) => `${entry?.registryProjectId} - ${entry?.name}`,
      value: (entry) => entry?.id,
    },
    width: GenericTableFieldSizeEnum.large,
    maxWidth: GenericTableFieldSizeEnum.xxxlarge,
    deriveDataValue: (row) => `${row?.project?.registryProjectId} - ${row?.project?.name}`,
    renderDataCell: (row) => (
      <>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack gap={1}>
            <Link
              href={`/projects/${row?.project?.id}`}
              style={{ color: COLORS.rubiconGreen, textUnderlineOffset: 3 }}
              onClick={(event) => event.stopPropagation()}
            >
              <Typography variant="body2" fontWeight={300}>
                {row?.project?.registryProjectId} - {row?.project?.name}
              </Typography>
            </Link>

            <Maybe condition={row?.project?.suspended}>
              <Grid container gap={1} alignItems="center">
                <SuspendedChip />
              </Grid>
            </Maybe>
          </Stack>
        </Stack>
      </>
    ),
  },
  {
    field: "developer",
    label: "Developer",
    width: GenericTableFieldSizeEnum.medium,
    maxWidth: GenericTableFieldSizeEnum.xxlarge,
  },
  {
    field: "floorPrice",
    label: "Floor Price",
    type: "money",
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "createdAt",
    label: "Created Date",
    type: "date",
    hide: (rows) => rows?.at(0)?.creating || rows?.at(0)?.editing,
    width: 130,
    fixedWidth: true,
    deriveDataValue: (row) => dateFormatter(row?.createdAt?.toString(), "MM/dd/yyyy hh:mm:ss a"),
  },
  {
    field: "feeStructure",
    label: "Marketing Fees",
    useRenderedAsEdit: true,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    exportable: false,
    formHelperText: ({ row, errors }): JSX.Element => {
      const _error = errors?.amends?.at(0)?.feeStructure;
      const hasError = !!_error;
      const isFeeStructureError = _error?.type === "validateFeeStructure";

      const error = hasError
        ? isFeeStructureError
          ? !row?.feeStructure?.length
            ? "Required"
            : _error?.message === "Required"
              ? "Required"
              : "Invalid fee structure"
          : errors?.amends?.at(0)?.feeStructure?.message
        : null;

      return <GenericTableFormErrorHelperText errors={[error]} />;
    },
    renderDataCell: (row): JSX.Element => <MarketingFeeButton row={row} />,
  },
  {
    field: "documents" as any,
    label: "Documents",
    useRenderedAsEdit: true,
    hide: (rows) => rows?.at(0)?.creating,
    width: GenericTableFieldSizeEnum.small,
    fixedWidth: true,
    exportable: false,
    renderDataCell: (row): JSX.Element => <GenericTableDocumentsButton row={{ ...row, alwaysEnabled: true }} />,
  },
  {
    field: "status",
    label: "Contract Status",
    type: "select",
    valueOptions: [
      {
        label: "Indicative",
        value: MarketingAgreementStatus.INDICATIVE,
      },
      {
        label: "Advanced",
        value: MarketingAgreementStatus.ADVANCED,
      },
      {
        label: "Signed",
        value: MarketingAgreementStatus.CONTRACTED,
      },
    ],
    transformDataValue: (value: MarketingAgreementStatus) =>
      value ? capitalize(value === MarketingAgreementStatus.CONTRACTED ? "Signed" : value) : "",
    renderDataCell: ({ status }): JSX.Element => {
      const [color, background] =
        status === MarketingAgreementStatus.CANCELED ? ["#D32F2F", "#D32F2F1A"] : ["#000000DE", "#00000014"];

      return (
        <Chip
          label={status ? capitalize(status === MarketingAgreementStatus.CONTRACTED ? "Signed" : status) : ""}
          sx={{ color, background }}
        />
      );
    },
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
  },
  {
    field: "lineItems",
    hide: true,
    exportable: true,
  },
  {
    field: "actions" as any,
    label: "Actions",
    type: "action",
    sortable: false,
    exportable: false,
    creatable: false,
    editable: false,
    width: GenericTableFieldSizeEnum.flexsmall,
    fixedWidth: true,
    renderDataCell: (row): JSX.Element => <ActionsCell row={row} />,
  },
];
