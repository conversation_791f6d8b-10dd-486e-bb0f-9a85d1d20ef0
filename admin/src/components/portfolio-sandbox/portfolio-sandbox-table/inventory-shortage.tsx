import React, { useMemo } from "react";
import { Stack, Tooltip, Typography } from "@mui/material";
import { convertStringToNumber, PortfolioRow, ProjectPopulationType } from "./portfolio-sandbox-model";
import AvailableShortage from "./available-Shortage";
import integerFormat from "@/utils/formatters/integerFormat";
import DetailsIcon from "@mui/icons-material/Info";
import Maybe from "@/components/maybe/maybe";

export default function InventoryShortage(props: {
  portfolioRow: PortfolioRow;
  availableAmount: number;
  projectsPopulationType?: ProjectPopulationType;
}): JSX.Element {
  const { availableAmount, portfolioRow, projectsPopulationType = ProjectPopulationType.NOT_APPLICABLE } = props;

  const isShowShowShortageDetails: boolean = useMemo(
    () => projectsPopulationType === ProjectPopulationType.AVAILABLE && !!portfolioRow?.vintageInterval?.value?.id,
    [portfolioRow, projectsPopulationType],
  );

  return (
    <Stack direction="row" gap={1}>
      <Stack direction="row" gap={0}>
        <Typography
          variant="body2"
          component="div"
          sx={{ color: `${+portfolioRow?.inventoryShortage?.value < 0 ? "red" : "black"}` }}
        >
          {integerFormat(portfolioRow?.inventoryShortage?.value)}
        </Typography>
        <Maybe condition={isShowShowShortageDetails}>
          <Tooltip placement="top" title={portfolioRow?.inventoryShortage?.tooltip}>
            <DetailsIcon sx={{ height: "20px", marginTop: "-10px" }} />
          </Tooltip>
        </Maybe>
      </Stack>
      <Maybe condition={isShowShowShortageDetails}>
        <AvailableShortage
          quantity={convertStringToNumber(portfolioRow?.amountAllocated?.value)}
          availableAmount={availableAmount}
        />
      </Maybe>
    </Stack>
  );
}
