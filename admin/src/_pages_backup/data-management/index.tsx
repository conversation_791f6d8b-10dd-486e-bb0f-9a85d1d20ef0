import Page from "@/components/layout/containers/page";
import { Authorize, ShowIfAuthorized } from "@/services/authorize";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

import SyncWidget from "./sync-widget";

export default function DataManagementPage(): JSX.Element {
  return (
    <Authorize permissions={[PermissionEnum.REPORTING_DATA_MANAGEMENT]}>
      <Page>
        <ShowIfAuthorized permissions={[PermissionEnum.DATA_MANAGEMENT_WRITE_SCIENCE]}>
          <div style={{ marginBottom: "10px" }}>
            <SyncWidget
              endpoint="reporting/sync/projects/science"
              title="Projects Sync: Science Team"
              spreadsheetId={process.env.RUBICON_ADMIN_GOOGLE_SHEETS_PROJECTS_SCIENCE}
              spreadsheetName="Project Data Sync Sheet (Science)"
            />
          </div>
        </ShowIfAuthorized>

        <ShowIfAuthorized permissions={[PermissionEnum.DATA_MANAGEMENT_WRITE_TRADING]}>
          <div>
            <SyncWidget
              endpoint="reporting/sync/projects/trading"
              title="Projects Sync: Trading"
              spreadsheetId={process.env.RUBICON_ADMIN_GOOGLE_SHEETS_PROJECTS_TRADING}
              spreadsheetName="Project Data Sync Sheet (Trading)"
            />
          </div>
        </ShowIfAuthorized>
      </Page>
    </Authorize>
  );
}
