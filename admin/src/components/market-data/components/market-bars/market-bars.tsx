import { Box, Stack, ToggleButton, ToggleButtonGroup } from "@mui/material";
import { useState } from "react";
import classes from "./styles.module.scss";
import MarketIndicies from "./market-indicies";
import Maybe from "@/components/maybe/maybe";
import { isEmpty } from "lodash";

const toggleButtonStyle = {
  "&.MuiButtonBase-root": {
    color: "#094436",
    backgroundColor: "white",
  },
  "&.MuiButtonBase-root: hover": {
    backgroundColor: "#D1E1CB",
  },
  "&.Mui-selected": {
    color: "white",
    backgroundColor: "#094436",
  },
  "&.Mui-selected: hover": {
    backgroundColor: "rgb(0 78 60)",
  },
};

const ResponseMapping = {
  "1D": "perc_change_1",
  "1W": "perc_change_7",
  "1M": "perc_change_30",
  "3M": "perc_change_90",
  YTD: "perc_change_ytd",
  "1Y": "perc_change_365",
};

const MarketBars = (): JSX.Element => {
  const [filter, setFilter] = useState<string>("1D");

  const handleChange = (event: React.MouseEvent<HTMLElement>, newFilter: string): void => {
    event.preventDefault();
    if (!!newFilter) setFilter(newFilter);
  };

  const toggleOptions = ["1D", "1W", "1M", "3M", "YTD", "1Y"];

  return (
    <Box className={classes.boxContainer}>
      <Box sx={{ textAlign: "right" }}>
        <ToggleButtonGroup
          color="primary"
          value={filter}
          exclusive
          onChange={handleChange}
          aria-label="Platform"
          sx={{ height: "35px" }}
        >
          {toggleOptions.map((option) => (
            <ToggleButton key={option} value={option} sx={toggleButtonStyle}>
              {option}
            </ToggleButton>
          ))}
        </ToggleButtonGroup>
      </Box>
      <Stack gap={2} mt={4} sx={{ width: "100%", overflow: "auto", paddingBottom: "10px" }}>
        <Maybe condition={!isEmpty(ResponseMapping[filter])}>
          <MarketIndicies filter={ResponseMapping[filter]} />
          {/* <TopMovers filter={ResponseMapping[filter]} /> */}
        </Maybe>
      </Stack>
    </Box>
  );
};

export default MarketBars;
