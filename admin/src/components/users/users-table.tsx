import React, { useState, useEffect, useContext, useMemo } from "react";
import {
  AdminUserQ<PERSON>y,
  AdminUserQueryResponse,
  PermissionEnum,
  UserRelations,
  uuid,
} from "@rubiconcarbon/shared-types";
import dateFormatterEST from "@/utils/formatters/estDateFormatter";
import { searchStringInArray } from "@/utils/helpers/general/general";
import UserSummary from "./user-summary";
import { CheckStatus } from "../ui/check-staus/check-status";
import ActionButton from "@/components/ui/action-button/action-button-enhanced";
import { UserData, mapUsersData } from "../../models/user-data";
import EnhancedTable, { ColDef, SortOrder } from "../ui/table/enhanced-table";
import { stringComparator } from "@/utils/comparators/comparator";
import TableBox from "../ui/table-box/table-box";
import useNavigation from "@/utils/hooks/useNavigation";
import { Box } from "@mui/material";
import IconStatusChip from "../ui/status-chip/IconStatusChip";
import UserStatusModal, { UserStatusInput } from "./user-status-modal";
import { AuthContext } from "@/providers/auth-provider";
import useBreadcrumbs from "@/providers/breadcrumb-provider";
import { USERS_PAGE_LABEL } from "@/constants/pages-labels";
import OrganizationName from "../ui/organization-name/organization-name";
import { UserRoleLabel } from "@/constants/user-role";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";

export default function UsersTable(): JSX.Element {
  const [users, setUsers] = useState<UserData[]>();
  const { pushOnToPath } = useNavigation();
  const [selectedUser, setSelectedUser] = useState<UserStatusInput>(null);
  const [isUserStatusModalOpen, setIsUserStatusModalOpen] = useState<boolean>(false);
  const { user: loginUser } = useContext(AuthContext);
  const { updateBreadcrumbName } = useBreadcrumbs();

  const isAuthorizedUpdate: boolean = useMemo(
    () => loginUser?.hasPermission(PermissionEnum.ORGANIZATIONS_MANAGE_USERS),
    [loginUser],
  );

  const getStatusChip = (inputMap: Map<string, any>): JSX.Element => {
    const userStatus = inputMap.get("status");
    const isEnabled = userStatus === "enabled" ? true : false;
    const id = inputMap.get("id");
    const userName = inputMap.get("name");
    const onOpenModalHandler = (): void => {
      if (isAuthorizedUpdate) {
        setSelectedUser({
          id: uuid(id),
          userName,
          isEnabled,
        });
        setIsUserStatusModalOpen(true);
      }
    };

    return (
      <Box sx={{ cursor: isAuthorizedUpdate ? "pointer" : "auto", width: "100px" }} onClick={onOpenModalHandler}>
        <IconStatusChip isShowIcon={false} isSuccess={isEnabled} label={isEnabled ? "Enabled" : "Disabled"} />
      </Box>
    );
  };

  const columnsDef: ColDef[] = [
    {
      columnName: "name",
      displayName: "Name",
      comparator: stringComparator,
    },
    {
      columnName: "organizationName",
      displayName: "Organization",
      comparator: stringComparator,
      formatter: {
        func: (x: any): JSX.Element => (
          <OrganizationName
            organization={{ name: x?.get("organizationName"), organization: { id: x?.get("organizationId") } } as any}
            style={{ fontSize: 14 }}
          />
        ),
        inputFields: ["organizationName", "organizationId"],
      },
    },
    {
      columnName: "organizationRole",
      displayName: "Roles",
      formatter: {
        func: (x: any): JSX.Element => <>{UserRoleLabel[x?.get("organizationRole")]}</>,
        inputFields: ["organizationRole"],
      },
    },
    {
      columnName: "email",
      displayName: "Email",
      comparator: stringComparator,
    },
    {
      columnName: "createdAt",
      displayName: "Create Date",
      formatter: { func: dateFormatterEST },
      exportFormatter: { func: dateFormatterEST },
    },
    {
      columnName: "status",
      displayName: "Status",
      formatter: {
        func: getStatusChip,
        inputFields: ["status", "id", "name"],
      },
    },
  ];

  const {
    data: rows,
    error,
    isLoading,
    mutate: refresh,
  } = useRequest<AdminUserQueryResponse, object, object, AdminUserQuery>({
    url: "/admin/users",
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeRelations: [UserRelations.ORGANIZATION],
    },
  });

  useEffect(() => {
    updateBreadcrumbName("Users", USERS_PAGE_LABEL);
  }, [updateBreadcrumbName]);

  useEffect(() => {
    if (rows?.data) {
      setUsers(mapUsersData(rows?.data?.filter((u) => !!u?.organization)));
    }
  }, [rows]);

  const saveUserStatusHandler = (): void => {
    setIsUserStatusModalOpen(false);
    refresh();
  };

  const closeModalHandler = (): void => {
    setIsUserStatusModalOpen(false);
  };

  const newUserHandler = (): void => {
    pushOnToPath("create-user");
  };

  const popExpandContent = (row: UserData): JSX.Element => {
    return <UserSummary user={row} />;
  };

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = rows?.data?.filter(
      (row) =>
        !!row?.organization &&
        (row.name?.toUpperCase().includes(searchString) ||
          row.email?.toUpperCase().includes(searchString) ||
          row.status?.toUpperCase().includes(searchString) ||
          searchStringInArray(searchString, row.permitRoles) ||
          searchStringInArray(searchString, row.organizationUserRoles) ||
          row.organization?.name?.toUpperCase().includes(searchString) ||
          dateFormatterEST(row.createdAt?.toString()).includes(searchString)),
    );

    setUsers(mapUsersData(filteredData));
  };

  const getSearchBarContent = (): JSX.Element => {
    return (
      <ActionButton onClickHandler={newUserHandler} requiredPermission={PermissionEnum.USERS_CREATE}>
        Create User
      </ActionButton>
    );
  };

  return (
    <CheckStatus data={rows} isLoading={isLoading} error={error}>
      <TableBox>
        {users && (
          <EnhancedTable
            name={"users_info"}
            columnsDef={columnsDef}
            data={users}
            expandedContent={popExpandContent}
            getFilteredData={getFilteredData}
            searchBarContent={getSearchBarContent}
            defaultSort={{ columnName: "createdAt", order: SortOrder.DESC }}
          />
        )}
      </TableBox>
      <UserStatusModal
        user={selectedUser}
        isOpen={isUserStatusModalOpen}
        onSave={saveUserStatusHandler}
        onClose={closeModalHandler}
      />
    </CheckStatus>
  );
}
