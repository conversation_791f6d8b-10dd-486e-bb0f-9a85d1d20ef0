import React, { useState, useContext, use<PERSON>allback, useMemo } from "react";
import { AxiosContext } from "@/providers/axios-provider";
import { ModelPortfolioResponse, ModelPortfolioQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { Box, Stack, IconButton, Typography } from "@mui/material";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import COLORS from "../../ui/theme/colors";
import ConfirmationModal, { ButtonDef } from "../../ui/dialogs/confirmation-dialog";
import DialogTheme from "../../ui/dialogs/dialog-themes";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { KeyedMutator } from "swr";
import PDFIcon from "../../icons/pdf-icon";
import { useLogger } from "@/providers/logging";
import useFileDownloader, { DownloadResult } from "@/utils/hooks/useFileDownloader";
import { AuthContext } from "@/providers/auth-provider";
import { useRouter } from "next/router";

export default function PortfolioSandboxActions(props: {
  id: string;
  name: string;
  allowExportPDF?: boolean;
  refreshPortfolios: KeyedMutator<ModelPortfolioQueryResponse>;
}): JSX.Element {
  const { id, name, allowExportPDF, refreshPortfolios } = props;
  const [isConfirmationOpen, setIsConfirmationOpen] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();
  const { download } = useFileDownloader();
  const { user: loginUser } = useContext(AuthContext);
  const { push } = useRouter();

  const allowDelete: boolean = useMemo(
    () => loginUser.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_DELETE),
    [loginUser],
  );

  const allowEdit: boolean = useMemo(
    () =>
      loginUser.hasPermissions([
        PermissionEnum.MODEL_PORTFOLIOS_COMPONENTS_WRITE,
        PermissionEnum.MODEL_PORTFOLIOS_UPDATE,
      ]),
    [loginUser],
  );

  const onConfirmDelete = useCallback(
    async (id: string) => {
      setIsConfirmationOpen(false);
      try {
        await api.delete<ModelPortfolioResponse>(`admin/model-portfolios/${id}`);
        enqueueSuccess("Successfully deleted portfolio");
        refreshPortfolios();
      } catch (error: any) {
        logger.error(`error deleting portfolio id: ${id}. Error: ${error?.message}`, {});
        enqueueError("Unable to delete portfolio");
      }
    },
    [setIsConfirmationOpen, refreshPortfolios, enqueueSuccess, enqueueError, logger, api],
  );

  const dialogButtons: ButtonDef[] = [
    {
      label: "Yes, proceed",
      variant: "contained",
      onClickHandler: () => onConfirmDelete(id),
      tooltip: "proceed with delete",
    },
  ];

  const deletePortfolioHandler = useCallback(() => {
    setIsConfirmationOpen(true);
  }, [setIsConfirmationOpen]);

  const editPortfolioHandler = useCallback(() => {
    push(`/inventory-management/portfolio-sandbox/${id}/edit-portfolio-sandbox`);
  }, [id, push]);

  const downloadPDFHandler = useCallback(async () => {
    const result: DownloadResult = await download(`portfolio_${name}`, `reporting/pdf/model-portfolio?id=${id}`);
    if (result?.isSuccess) {
      enqueueSuccess("PDF exported successfully");
    } else {
      enqueueError("Unable to download PDF");
    }
  }, [id, name, download, enqueueSuccess, enqueueError]);

  return (
    <>
      <Stack direction="row" gap={2}>
        <Box>
          <IconButton
            disabled={!allowEdit}
            sx={{ color: COLORS.rubiconGreen }}
            edge="start"
            onClick={editPortfolioHandler}
          >
            <CreateIcon />
          </IconButton>
        </Box>
        <Box>
          <IconButton
            disabled={!allowDelete}
            sx={{ color: COLORS.rubiconGreen }}
            edge="start"
            onClick={deletePortfolioHandler}
          >
            <DeleteIcon />
          </IconButton>
        </Box>
        <Box>
          <IconButton
            sx={{ color: COLORS.rubiconGreen, paddingTop: "12px" }}
            edge="start"
            disabled={!allowExportPDF}
            onClick={downloadPDFHandler}
          >
            <PDFIcon disabled={!allowExportPDF} />
          </IconButton>
        </Box>
      </Stack>
      <ConfirmationModal
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        title={"Please confirm"}
        dialogButtons={dialogButtons}
        dialogTheme={DialogTheme.INFO}
      >
        <Typography variant="body1" component="p" sx={{ fontWeight: 500 }}>
          You are about to delete <b>{name}</b> portfolio.
        </Typography>
      </ConfirmationModal>
    </>
  );
}
