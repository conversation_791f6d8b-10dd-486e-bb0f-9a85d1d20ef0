import {
  DocumentType,
  PermissionEnum,
  VintageAssetResponse,
  RetirementRelations,
  RetirementResponse,
  RetirementStatus,
} from "@rubiconcarbon/shared-types";
import React, { useMemo } from "react";
import { Grid, Card, CardContent } from "@mui/material";
import ItemDetails from "../../ui/details/item-details";
import StatusChip from "../../ui/status-chip/StatusChip";
import RetirementDetailsTable from "./retirement-details-table";
import PublicStatus from "../../ui/public-status/public-status";
import BackButton from "../../ui/back-button/back-button";
import { CheckStatus } from "../../ui/check-staus/check-status";
import { UploadFile } from "@mui/icons-material";
import { MISSING_DATA } from "@/constants/constants";
import CustomButton from "../../ui/custom-button/custom-button";
import useNavigation from "@/utils/hooks/useNavigation";
import useRequest from "@/utils/hooks/useRequest";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { useLogger } from "@/providers/logging";
import useDocumentsApi from "@/utils/hooks/useDocumentsApi";
import usePerformantEffect from "@/utils/hooks/usePerformantEffect";
import ProductName from "../../ui/product-name/product-name";
import { Maybe, numberFormat } from "@rubiconcarbon/frontend-shared";
import Link from "next/link";
import RetirementAttachment from "./retirement-attachment";
import { toRetirementModel } from "@/utils/helpers/transaction/to-transaction-models";
import { AllTransactionType } from "@/models/transaction";
import OrganizationName from "@/components/ui/organization-name/organization-name";

import classes from "../styles/retirement-item.module.scss";

const RetirementItem = (props: { retirementId: string | string[] }): JSX.Element => {
  const { retirementId } = props;
  const { currentPath, pushOnToPath } = useNavigation();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const {
    data,
    error,
    isLoading,
    mutate: refreshRetirementData,
  } = useRequest<RetirementResponse>({
    url: `/admin/retirements/{id}`,
    pathParams: {
      id: retirementId,
    },
    queryParams: {
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
        RetirementRelations.LINKS,
        RetirementRelations.REQUESTED_BY,
      ],
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch retirement/transfer.");
        logger.error(`Unable to fetch retirement/transfer with id ${retirementId}. Error: ${error?.message}`, {});
      },
    },
  });

  const { id, uiKey, amount, status, type, assets, retirement, transfer, memo, dateStarted, dateFinished } =
    toRetirementModel(data) || {};

  const isTransfer = useMemo(() => type === AllTransactionType.TRANSFER_OUTFLOW, [type]);
  const organization = useMemo(
    () => (isTransfer ? transfer : retirement)?.organization,
    [isTransfer, retirement, transfer],
  );
  const isPortfolioAssets = useMemo(() => Object.hasOwn(assets?.at?.(0) || {}, "rct"), [assets]);
  const associatedVintages = useMemo(
    () =>
      assets?.reduce(
        (vintages: VintageAssetResponse[], asset) => [
          ...vintages,
          ...(isPortfolioAssets ? (asset?.associatedVintages ?? []) : [asset as unknown as VintageAssetResponse]),
        ],
        [],
      ),
    [assets, isPortfolioAssets],
  );

  const {
    documents: [retirementCertificate],
    fetching,
    fetch,
  } = useDocumentsApi({
    query: {
      relatedUiKey: uiKey,
      types: [DocumentType.RETIREMENT_CERTIFICATE],
    },
  });

  usePerformantEffect(() => {
    if (!fetching && !!organization?.id && !!uiKey) {
      setTimeout(async () => await fetch());
    }
  }, [organization?.id, uiKey]);

  const canUploadRetirementCertificate = useMemo(
    () => !isTransfer && !retirementCertificate && status === RetirementStatus.PROCESSING,
    [isTransfer, retirementCertificate, status],
  );

  const retirementCompHandler = (): void => {
    pushOnToPath(`retirement-composition`);
  };

  return (
    <CheckStatus data={data} isLoading={isLoading} error={error}>
      <Grid container spacing={0} direction="column" alignItems="center" gap={2}>
        <Maybe condition={!!data}>
          <Card variant="elevation" sx={{ width: "100%", borderRadius: 4, backgroundColor: "#FAFAFA" }}>
            <CardContent>
              <Grid container spacing={0}>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Type:" value={isTransfer ? "Transfer" : "Retirement"} sx={{ paddingBottom: 1 }} />
                </Grid>
              </Grid>
              <Grid container spacing={0}>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Status:" value={<StatusChip status={status} />} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails
                    label="Product:"
                    value={<ProductName assets={assets} style={{ fontSize: "0.875rem" }} />}
                    sx={{
                      alignItems: "center",
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Date/Time Requested:" value={dateStarted ?? MISSING_DATA} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Transaction Key:" value={uiKey} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Amount of Credits:" value={numberFormat(amount)} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails label="Date/Time Completed:" value={dateFinished ?? MISSING_DATA} />
                </Grid>
                <Grid item xs={12} md={4}>
                  <ItemDetails
                    label="Organization:"
                    value={
                      !!organization ? (
                        <OrganizationName organization={organization} style={{ fontSize: 14 }} />
                      ) : (
                        MISSING_DATA
                      )
                    }
                  />
                </Grid>
                <Maybe condition={!isTransfer}>
                  <Grid item xs={12} md={4}>
                    <ItemDetails label="Beneficiary:" value={retirement?.beneficiary ?? MISSING_DATA} />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <ItemDetails label="Public/Private:" value={<PublicStatus isPublic={retirement?.isPublic} />} />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <ItemDetails label="Memo:" value={memo ?? MISSING_DATA} />
                  </Grid>
                  <Grid item xs={12} md={4}></Grid>
                  <Maybe condition={canUploadRetirementCertificate}>
                    <Grid item container xs={12} md={4} justifyContent="start" alignItems="center">
                      <Link href={`${currentPath}/upload-retirement-certification`} className={classes.Link}>
                        <UploadFile fontSize="small" /> <span>upload retirement certification</span>
                      </Link>
                    </Grid>
                  </Maybe>
                </Maybe>
                <Maybe condition={isTransfer}>
                  <Grid item xs={12} md={4}>
                    <ItemDetails
                      label="Transfer Credits to Account:"
                      value={transfer?.registryAccount ?? MISSING_DATA}
                    />
                  </Grid>
                </Maybe>
                <Maybe condition={!!retirementCertificate}>
                  <Grid item container xs={7} sm={4} md={3} lg={2.5} justifyContent="start" alignItems="center">
                    <RetirementAttachment
                      retirementCertificate={retirementCertificate}
                      renderFileAs="button"
                      onDelete={fetch}
                    />
                  </Grid>
                </Maybe>
              </Grid>
              <Grid container spacing={0}>
                <Grid item={true} xs={12} mt={1}>
                  <Grid container direction="row" justifyContent="flex-start" alignItems="flex-start"></Grid>
                </Grid>
                <Grid container spacing={0} mt={3}>
                  <Grid item={true} xs={12}>
                    <BackButton popInstead />
                    <Maybe condition={status === RetirementStatus.PORTFOLIO_MANAGER_REVIEW && isPortfolioAssets}>
                      <CustomButton
                        requiredPermission={PermissionEnum.RETIREMENTS_CLEAR_PM_REVIEW}
                        onClickHandler={retirementCompHandler}
                        style={{ height: "30px", marginLeft: "10px" }}
                      >
                        Edit Composition
                      </CustomButton>
                    </Maybe>
                  </Grid>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Maybe>

        <Maybe
          condition={
            status !== RetirementStatus.CANCELED &&
            !!associatedVintages?.length &&
            Object?.hasOwn(associatedVintages?.at(0), "projectVintage")
          }
        >
          <RetirementDetailsTable id={id} rows={associatedVintages} refreshRetirementData={refreshRetirementData} />
        </Maybe>
      </Grid>
    </CheckStatus>
  );
};

export default RetirementItem;
