import { PropsWith<PERSON><PERSON>dren, useEffect } from "react";
import { Box } from "@mui/material";
import Spinner from "@/components/ui/spinner/spinner";
import NoData from "../no-data/no-data";
import { handleResponseError } from "@/utils/handle-response-error";
import { isEmpty, isNil } from "lodash";

interface CheckStatusProps {
  data?: any | any[];
  isLoading?: boolean | boolean[];
  error?: (any | undefined)[];
}

interface QueryResponse {
  data: any;
}

function instanceOfQueryResponse(object: any): object is QueryResponse {
  return "data" in object;
}

const isOneFalse = (input: any | any[]): boolean => {
  if (Array.isArray(input)) {
    return input.some((e) => !e);
  }
  return !input;
};

const getFirstExisting = (input: any | any[]): any => {
  if (Array.isArray(input)) {
    return input.find((e) => !!e);
  }
  return !!input ? input : null;
};

const isAllFalse = (input: any | any[]): boolean => {
  if (Array.isArray(input)) {
    return input.every((e) => !e);
  }
  return !input;
};

const isEmptyResponse = (input: any): boolean => {
  if (!isNil(input) && instanceOfQueryResponse(input)) return isEmpty(input?.data) ? true : false;

  return !isNil(input) && isEmpty(input) ? true : false;
};

const isAllEmpty = (input: any | any[]): boolean => {
  if (isNil(input)) return false;
  if (isEmpty(input)) return true;
  if (Array.isArray(input)) return input.every((e) => isEmptyResponse(e));

  return instanceOfQueryResponse(input) && isEmpty(input?.data) ? true : false;
};

export function CheckStatus({
  children,
  data,
  isLoading = true,
  error,
}: PropsWithChildren<CheckStatusProps>): JSX.Element {
  useEffect(() => {
    if (isOneFalse(error)) {
      handleResponseError(getFirstExisting(error));
    }
  }, [error]);

  if (!isOneFalse(isLoading))
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <Spinner />
      </Box>
    );

  if (isAllEmpty(data) && isAllFalse(isLoading)) {
    return <NoData />;
  }

  return <>{children}</>;
}
