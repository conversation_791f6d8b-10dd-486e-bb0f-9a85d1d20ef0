import { Maybe } from "@rubiconcarbon/shared-types";

type Path = string | number;
type NestedPick<T, K extends Path> = K extends `${infer First}.${infer Rest}`
  ? First extends keyof T
    ? { [P in First]: NestedPick<T[First], Rest> }
    : never
  : K extends keyof T
    ? { [P in K]: T[K] }
    : never;

/**
 * pickFromArrayOfRecords
 *
 * picks properties in an array of objects.
 * it is functional similar to lodash pluck but without the need to load the whole library
 *
 * @param source | array of objects
 * @param keys | comma separates strings. it is not an array but rather a spread of the keys
 * @returns | array of objects with just the keys passed
 */
export const pickFromArrayOfRecords = <T, K extends Path>(source: T[], ...keys: K[]): NestedPick<T, K>[] =>
  source.map((record) => {
    const result: any = {};

    for (const key of keys) {
      let current: any = record;

      for (const part of key.toString().split(".")) {
        if (current === undefined) break;
        current = current[part];
      }
      if (current !== undefined) result[key] = current;
    }
    return result;
  });

/**
 * pickFromRecord
 *
 * picks properties in an objects.
 * it is functional similar to lodash pluck but without the need to load the whole library
 *
 * @param source | object
 * @param keys | comma separates strings. it is not an array but rather a spread of the keys
 * @returns | object with just the keys passed
 */
export const pickFromRecord = <T, K extends Path>(source: T, ...keys: K[]): NestedPick<T, K> => {
  const result: any = {};

  for (const key of keys) {
    let current: any = source;

    for (const part of key.toString().split(".")) {
      if (current === undefined) break;
      current = current[part];
    }
    if (current !== undefined) result[key] = current;
  }
  return result;
};

/**
 * uniqueByKeys
 *
 * outputs an array of unique objects based on the keys passed
 *
 * @param array | array of objects
 * @param keys | array of keys to enforce uniqueness. it can be a single string or a nested string using dot notation. eg: 'name', 'address.city'
 * @returns | array containing the unique objects based on the keys
 */
export const uniqueByKeys = <T = Record<string, any>>(array: T[] = [], keys: string[]): T[] => {
  const uniqueMap = new Map();

  return array.reduce((accum, record) => {
    const compositeKey = keys
      .map((key) =>
        key.split(".").reduce((nestedAccum, nestedKey) => {
          const parsedNestedAccum: Maybe<Record<string, any>> = JSON.parse(nestedAccum);

          return !!parsedNestedAccum ? JSON.stringify(parsedNestedAccum[nestedKey]) : "";
        }, JSON.stringify(record)),
      )
      .join("-");

    if (!uniqueMap.has(compositeKey)) {
      uniqueMap.set(compositeKey, true);
      accum.push(record);
    }

    return accum;
  }, []);
};

/**
 * searchByKeys
 *
 * does a fuzzy (regex) search in an array of objects for a passed in search token.
 * the function searches for the search token on the properties passed in as "keys".
 * the key can be single or multi level as long as the path is traversable.
 *
 * @param searchToken | value to search for
 * @param items | array of object to search in
 * @param keys | array of string values that signifies which properties in the array of objects to search on. it can be single or multi level. eg. ["name", "country.capital"]
 * @param matchRegex | the regex to used for the comparison / matching. it defaults to "includes with case insensitive"
 * @returns | objects that return true for the search token
 */
export const searchByKeys = <T = Record<string, any>>(
  searchToken: string,
  items: T[],
  keys: string[],
  matchRegex: RegExp = /.*/i,
): T[] => {
  if (!searchToken) return items;

  return items.filter((item) => {
    const verdicts = [];

    for (const key of keys) {
      const [first, ...rest] = key.split(".");

      let nestedItem = item[first];
      const restCopy = Object.assign([], rest);

      if (Array.isArray(nestedItem) && restCopy.length > 0) {
        verdicts.push(...searchByKeys(searchToken, nestedItem, [restCopy.join(".")], matchRegex));
      } else {
        while (nestedItem && restCopy.length > 0) {
          const token = restCopy.shift();
          nestedItem = nestedItem[token];
        }

        if (nestedItem) {
          const asString = nestedItem.toString();
          verdicts.push(matchRegex.test(asString) && asString.toLowerCase().includes(searchToken.toLowerCase()));
        }
      }
    }

    return verdicts.some((verdict) => !!verdict);
  });
};

/**
 * deepClone
 *
 * return a non-referential clone of an object recursively
 *
 * @param data | the object to clone
 * @returns | the exact passed in object without a reference to the passed in object
 */
export const deepClone = <T>(data: T): T => {
  if (data === null || typeof data !== "object") return data;

  if (Array.isArray(data)) return data.map((item) => deepClone(item)) as T;

  const cloned = {} as T;
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      cloned[key] = deepClone(data[key]);
    }
  }

  return cloned;
};

export const isEmptyObject = (record: Record<string | number, any>): boolean => !Object.keys(record).length;
