import { CompareArrowsRounded, FileDownloadRounded } from "@mui/icons-material";
import {
  AssetType,
  BookRelations,
  BookUpdateRequest,
  GroupingParentQueryResponse,
  GroupingParentResponse,
  PermissionEnum,
  uuid,
} from "@rubiconcarbon/shared-types";
import { useState } from "react";
import { BOOKS_COLUMNS } from "./constants/books-columns";

import GenericTable from "../ui/generic-table";
import { DEFAULT_EXPORT_STYLE } from "../ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility, { Values } from "../ui/generic-table/hooks/use-generic-table-utility";
import { GenericTableRowModel } from "../ui/generic-table/types/generic-table-row-model";
import { useLogger } from "@/providers/logging";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";

import useNavigation from "@/utils/hooks/useNavigation";
import BookValidation from "../alerts/book-validation/book-validation";
import { Stack } from "@mui/material";
import { BookParentGrouping } from "./types/book";
import { calculator, toDecimal, useRequest, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";

const Books = (): JSX.Element => {
  const { logger } = useLogger();
  const { pushOnToPath } = useNavigation();
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [bookId, setBookId] = useState<uuid>();
  const [requestBody, setRequestBody] = useState<BookUpdateRequest>();

  const {
    data: booksByParentResponse,
    isLoading: loadingBooksByParents,
    isValidating: reloadingBooksByParents,
    mutate: refreshBooksByParents,
  } = useRequest<GroupingParentQueryResponse>({
    url: "/admin/books/parents",
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      isEnabled: true,
      includeTotalCount: true,
      includeRelations: [
        BookRelations.OWNER_ALLOCATIONS,
        BookRelations.OWNER_ALLOCATIONS_BY_ASSET_TYPE,
        BookRelations.PRICES,
      ],
    },
    swrOptions: {
      onError: (error: any): void => {
        enqueueError("Unable to load books");
        logger.error(`Unable to load books by parents: ${error?.message}`, {});
      },
    },
  });

  const { trigger: updateBook } = useTriggerRequest({
    url: "/admin/books/{id}",
    method: "patch",
    pathParams: {
      id: bookId,
    },
    requestBody,
    swrOptions: {
      onSuccess: (): void => {
        enqueueSuccess("Successfully updated book");
        setTimeout(async () => await refreshBooksByParents());
      },
      onError: (error: any): void => {
        enqueueError("Unable to update book");
        logger.error(`Unable to update book with id ${bookId}: ${error?.message}`, {});
      },
    },
  });

  const { form, table } = useGenericTableUtility<BookParentGrouping>({});

  const { handleSubmit } = form || {};

  const toRowModel = (grouping: GroupingParentResponse): GenericTableRowModel<BookParentGrouping> => {
    // only need vintage asset type
    const vintageAllocation = grouping?.ownerAllocationsByAssetType?.find(
      ({ assetType }) => assetType === AssetType.REGISTRY_VINTAGE,
    );

    return {
      compositeId: grouping?.books?.map(({ id }) => id)?.join(","),
      ...grouping,
      ownerAllocationsByAssetType: [vintageAllocation],
      total: calculator(vintageAllocation?.groupedPrices?.totalPriceAllocated)
        .add(vintageAllocation?.groupedPrices?.totalPricePendingBuy)
        .subtract(vintageAllocation?.groupedPrices?.totalPricePendingSell)
        .calculate()
        .toNumber(),
    };
  };

  const onSubmit = async (formData: Values<BookParentGrouping>): Promise<void> => {
    const {
      amends: [{ books, limit }],
    } = formData;

    setBookId(books?.at(0)?.id as uuid);

    if (limit.holdingAmountMax === undefined) limit.holdingAmountMax = null;

    setRequestBody({
      ...books?.at(0),
      limit: {
        ...limit,
        holdingPriceMax: toDecimal(limit?.holdingPriceMax as any),
        holdingPriceMin: toDecimal(limit?.holdingPriceMin as any),
      },
    });
    setTimeout(async () => await updateBook());
  };

  return (
    <Stack gap={1}>
      <BookValidation books={booksByParentResponse?.data} />
      <GenericTable
        id="Books"
        loading={loadingBooksByParents}
        reloadingRow={reloadingBooksByParents}
        columns={BOOKS_COLUMNS}
        pageableData={booksByParentResponse}
        sort={{
          sorts: {
            total: "desc",
          },
        }}
        toRowModel={toRowModel}
        export={{
          filename: `Book-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.BOOKS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: !booksByParentResponse?.data?.length,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Transfer Assets",
            startIcon: <CompareArrowsRounded />,
            requiredPermission: PermissionEnum.TRANSFERS_EXECUTE,
            onClickHandler: () => pushOnToPath("/transfer-assets"),
          },
        ]}
        resetForm={table?.resetForm}
        useForm={table?.useForm}
        bindAddRow={table?.bindAddRow}
        onFormSubmit={handleSubmit(onSubmit)}
      />
    </Stack>
  );
};

export default Books;
