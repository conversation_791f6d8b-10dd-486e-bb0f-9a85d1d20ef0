import { AxiosContext } from "@/providers/axios-provider";
import { useContext } from "react";
import { KeyedMutator } from "swr/_internal";
import {
  GenericRecord,
  Undefinable,
  useBatchRequest as useSharedBatchRequest
} from "@rubiconcarbon/frontend-shared";
import { Method } from "../helpers/general/fetch";

// Original types for backward compatibility
export type BatchRequest<
  ResponseType,
  RequestBody = GenericRecord,
  PathParams = GenericRecord,
  QueryParams = GenericRecord,
> = {
  url: string;
  method?: Method;
  pathParams?: PathParams;
  queryParams?: QueryParams;
  requestBody?: RequestBody;
  swrOptions?: any;
};

export type UseBatchRequestFinally<ResponseType> = { data: ResponseType[]; errors: any };

export type UseBatchRequestFinallyFunction<ResponseType> = (result: UseBatchRequestFinally<ResponseType>) => void;

type UseBatchRequestReturn<ResponseType> = {
  data: ResponseType[];
  errors: any[];
  isLoading: boolean;
  isValidating: boolean;
  mutate: KeyedMutator<ResponseType[]>;
};

/**
 * A wrapper around the frontend-shared useBatchRequest hook
 * This maintains backward compatibility with existing code
 */
const useBatchRequest = <
  ResponseType,
  RequestBody = GenericRecord,
  PathParams = GenericRecord,
  QueryParams = GenericRecord,
>(
  requests: BatchRequest<ResponseType, RequestBody, PathParams, QueryParams>[],
  requestFinally?: UseBatchRequestFinallyFunction<ResponseType>,
): UseBatchRequestReturn<ResponseType> => {
  const { api } = useContext(AxiosContext);

  // Use the shared library hook with mapped props
  const result = useSharedBatchRequest<ResponseType, RequestBody, PathParams, QueryParams>(
    // Map the requests to the format expected by the shared hook
    requests.map(request => ({
      ...request,
      // Use the default instance from the context
      instance: "default",
    })),
    // Use the default instance
    "default",
    // No dependencies
    undefined,
    // No global condition
    undefined,
    // Map the requestFinally callback
    requestFinally ? (result) => requestFinally(result) : undefined
  );

  // Return only the properties expected by the original hook
  return {
    data: result.data as ResponseType[],
    errors: result.errors,
    isLoading: result.isLoading,
    isValidating: result.isValidating,
    mutate: result.mutate,
  };
};

export default useBatchRequest;
