import BidAsk from "@/components/bid-ask";
import Page from "@/components/layout/containers/page";
import { Authorize } from "@/services/authorize";
import { PermissionEnum } from "@rubiconcarbon/shared-types";

export default function BidAskPage(): JSX.Element {
  return (
    <Authorize permissions={[PermissionEnum.REPORTING_MARKET_DATA]}>
      <Page>
        <BidAsk />
      </Page>
    </Authorize>
  );
}
