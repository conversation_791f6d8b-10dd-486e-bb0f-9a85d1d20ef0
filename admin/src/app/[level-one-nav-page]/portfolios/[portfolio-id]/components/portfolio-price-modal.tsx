import React, { useState, BaseSyntheticEvent, useContext, useEffect, useMemo, useCallback } from "react";
import { AxiosContext } from "@providers/axios-provider";
import { Box, TextField, Button, Dialog, DialogContent, DialogActions, Typography, Stack } from "@mui/material";
import { AdminBookResponse, uuid } from "@rubiconcarbon/shared-types";
import { NumericFormat } from "react-number-format";
import { convertPriceToNumber, convertStringToNumber } from "@utils/helpers/general/general";
import CheckIcon from "@mui/icons-material/Check";
import { isNothing, Maybe } from "@rubiconcarbon/frontend-shared";
import SummaryGroup, { itemGroup } from "@components/ui/summary-group/item-group";
import ActionButton from "@components/ui/action-button/action-button-enhanced";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import currencyFormat from "@utils/formatters/currency-format";
import { isEmpty } from "lodash";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import Decimal from "decimal.js";
import { MISSING_DATA } from "@constants/constants";
import PortfolioName from "@components/ui/portfolio-name/portfolio-name";
import { getPortfolioColor } from "@utils/helpers/portfolio/get-portfolio-helper";
import { BaseDialogProps } from "@models/dialogs";

import classes from "../styles/basket-price-modal.module.scss";

interface IFormField {
  error?: boolean;
  message?: string;
  value?: string;
}

export enum StepsStatus {
  "DONE",
  "CURRENT",
  "TODO",
}

export interface Indicator {
  name: string;
  label?: string;
  status: StepsStatus;
}

const supportingTextStyle = {
  color: "rgba(0, 0, 0, 0.6)",
  backgroundColor: "white",
  fontWeight: 400,
  fontSize: "12px",
  width: "501px",
  border: "none",
};

const getStepClass = (stepStatus: StepsStatus): string => {
  switch (stepStatus) {
    case StepsStatus.CURRENT:
      return "buttonStepNormal";
    case StepsStatus.TODO:
      return "buttonStepDisabled";
    default:
      return "buttonStepNormal";
  }
};

interface BookEditPriceModalProps extends BaseDialogProps {
  currentBasketPrice: number;
  currentRiskAdjPrice?: number | null;
  bookId: uuid;
  book: AdminBookResponse;
  algoPrice?: Decimal;
  algoPriceWithRisk?: Decimal;
  onSave: () => void;
}

export default function BookPriceModal({
  isOpen,
  bookId,
  onClose,
  onSave,
  book,
  algoPrice,
  algoPriceWithRisk,
}: BookEditPriceModalProps): JSX.Element | null {
  const [newBasketPrice, setNewBasketPrice] = useState<string>("");
  const [currentBasketPrice, setCurrentBasketPrice] = useState<string>("");
  const [riskAdjPrice, setRiskAdjPrice] = useState<string>("");
  const [currentRiskAdjPrice, setCurrentRiskAdjPrice] = useState<string>("");
  const [isConfirmation, setIsConfirmation] = useState<boolean>(false);
  const { api } = useContext(AxiosContext);
  const { enqueueSuccess, enqueueError } = useSnackbarVariants();

  const [riskPremiumPercent, setRiskPremiumPercent] = useState<IFormField>({
    error: false,
    message: "",
    value: "",
  });

  const [riskDollarAmount, setRiskDollarAmount] = useState<IFormField>({
    error: false,
    message: "",
    value: "",
  });

  const [steps] = useState<Indicator[]>([
    { name: "1", label: "Set the price", status: StepsStatus.CURRENT },
    { name: "2", label: "Enter risk adjustment premium percentage or dollar amount", status: StepsStatus.CURRENT },
    { name: "3", label: "Review new price outcome", status: StepsStatus.CURRENT },
  ]);

  useEffect(() => {
    if (book !== null) {
      if (!isNothing(book.purchasePriceWithBuffer)) {
        setRiskAdjPrice(book.purchasePriceWithBuffer!.toFixed(2));
        setCurrentRiskAdjPrice(book.purchasePriceWithBuffer!.toFixed(2));
      }

      if (!isNothing(book.purchasePrice)) {
        setNewBasketPrice(book.purchasePrice!.toFixed(2));
        setCurrentBasketPrice(book.purchasePrice!.toFixed(2));
      }
    }
  }, [book]);

  const algoHeader = useMemo(
    () => (
      <Typography variant="body2" component="p" sx={supportingTextStyle}>
        {`Algorithmic price: ${!!algoPrice && algoPrice.isFinite() ? currencyFormat(+algoPrice) : MISSING_DATA} | Algorithmic price with risk adjustment: ${!!algoPriceWithRisk && algoPriceWithRisk.isFinite() ? currencyFormat(+algoPriceWithRisk) : MISSING_DATA}`}
      </Typography>
    ),
    [algoPrice, algoPriceWithRisk],
  );

  const isValidBasketPrice = useCallback((): boolean => {
    if (newBasketPrice && convertPriceToNumber(newBasketPrice) <= 0) return false;

    return true;
  }, [newBasketPrice]);

  const getBasketPriceHelperText = useCallback((): string => {
    if (!isValidBasketPrice()) return "Price must be greater than zero";

    return "";
  }, [isValidBasketPrice]);

  const onSubmitHandler = useCallback(
    (event: BaseSyntheticEvent): void => {
      event.preventDefault();
      if (isValidBasketPrice()) {
        setIsConfirmation(true);
      }
    },
    [isValidBasketPrice],
  );

  const onCloseHandler = useCallback((): void => {
    setNewBasketPrice(currentBasketPrice);
    clearFields();
    setRiskAdjPrice(currentRiskAdjPrice);
    setIsConfirmation(false);
    onClose();
  }, [currentBasketPrice, currentRiskAdjPrice, onClose]);

  const customerPortalPriceGroup: itemGroup = useMemo(
    () => ({
      groupTitle: "Customer Portal Price",
      items: [
        {
          label: "Price",
          value: isEmpty(newBasketPrice) ? "-" : (currencyFormat(convertPriceToNumber(newBasketPrice)) ?? MISSING_DATA),
        },
        {
          label: "Risk Adjustment",
          value: isEmpty(riskAdjPrice) ? "-" : (currencyFormat(convertPriceToNumber(riskAdjPrice)) ?? MISSING_DATA),
        },
      ],
      groupStyle: {
        titleStyle: { fontSize: "16px" },
        itemStyle: { fontSize: "14px !important" },
        valueStyle: { fontSize: "24px !important" },
        containerStyle: { backgroundColor: "rgba(250, 250, 250, 1)" },
      },
    }),
    [newBasketPrice, riskAdjPrice],
  );

  const confirmationPriceGroup: itemGroup = useMemo(
    () => ({
      items: [
        {
          label: "Price",
          value: isEmpty(newBasketPrice)
            ? "-"
            : currentBasketPrice == newBasketPrice
              ? `${currencyFormat(convertStringToNumber(currentBasketPrice))}`
              : `${currencyFormat(convertStringToNumber(currentBasketPrice))} → ${currencyFormat(convertStringToNumber(newBasketPrice))}`,
        },
        {
          label: "Risk Adjustment",
          value: isEmpty(currentRiskAdjPrice)
            ? (currencyFormat(convertStringToNumber(riskAdjPrice)) ?? MISSING_DATA)
            : currentRiskAdjPrice === riskAdjPrice
              ? (currencyFormat(convertStringToNumber(riskAdjPrice)) ?? MISSING_DATA)
              : `${currencyFormat(convertStringToNumber(currentRiskAdjPrice))} → ${currencyFormat(convertStringToNumber(riskAdjPrice))}`,
        },
      ],
      groupStyle: {
        itemStyle: { fontSize: "14px !important" },
        valueStyle: { fontSize: "24px !important" },
        containerStyle: { backgroundColor: "rgba(250, 250, 250, 1)", paddingTop: "0px", width: "100%" },
      },
    }),
    [currentBasketPrice, currentRiskAdjPrice, newBasketPrice, riskAdjPrice],
  );

  const clearFields = (): void => {
    setRiskPremiumPercent({
      value: "",
      message: "",
      error: false,
    });

    setRiskDollarAmount({
      value: "",
      message: "",
      error: false,
    });

    setRiskAdjPrice("");
  };

  const onBasketPriceChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setNewBasketPrice(event.target.value);
    clearFields();
  };

  const onPercentageAmountChanged = (event: React.ChangeEvent<HTMLInputElement>): void => {
    if (isEmpty(event.target.value)) {
      clearFields();
      return;
    }
    setRiskPremiumPercent({
      ...riskPremiumPercent,
      value: event.target.value,
    });

    const newAmount = (convertStringToNumber(event.target.value) * convertStringToNumber(newBasketPrice)) / 100;
    setRiskDollarAmount({
      ...riskDollarAmount,
      value: ((convertStringToNumber(event.target.value) * convertStringToNumber(newBasketPrice)) / 100).toFixed(2),
    });

    setRiskAdjPrice((convertStringToNumber(newBasketPrice) + newAmount).toFixed(2));
  };

  const onDollarAmountChanged = (event: React.ChangeEvent<HTMLInputElement>): void => {
    if (isEmpty(event.target.value)) {
      clearFields();
      return;
    }

    setRiskDollarAmount({
      ...riskPremiumPercent,
      value: event.target.value,
    });

    setRiskPremiumPercent({
      ...riskDollarAmount,
      value: ((convertStringToNumber(event.target.value) * 100) / convertStringToNumber(newBasketPrice)).toFixed(2),
    });

    setRiskAdjPrice((convertStringToNumber(newBasketPrice) + convertStringToNumber(event.target.value)).toFixed(2));
  };

  const submitBasketPrice = useCallback((): void => {
    const payload = {
      purchasePrice: convertStringToNumber(newBasketPrice),
      purchasePriceWithBuffer: convertStringToNumber(riskAdjPrice),
    };

    api
      .patch<AdminBookResponse>(`admin/books/${bookId}`, payload)
      .then(() => {
        enqueueSuccess("Successfully updated portfolio price");
        clearFields();
        setIsConfirmation(false);
        onSave();
      })
      .catch((error: any) => {
        if (error?.response?.data?.message) {
          enqueueError(
            Array.isArray(error.response.data.message)
              ? error.response.data.message.join(", ")
              : error.response.data.message,
          );
        } else enqueueError("Sorry, we are unable to complete your request");
      });
  }, [api, bookId, enqueueError, enqueueSuccess, newBasketPrice, onSave, riskAdjPrice]);

  const dialogHead = useMemo(
    () => (
      <Box mt={-2}>
        <Typography
          variant="body2"
          component="p"
          sx={{
            color: "rgba(0, 0, 0, 1)",
            backgroundColor: "white",
            fontWeight: 500,
            fontSize: "24px",
            width: "501px",
            border: "none",
          }}
        >
          {isConfirmation ? "Confirm Customer Portal Price" : "Set Customer Portal Price"}
        </Typography>

        <Box sx={{ paddingTop: "5px" }}>
          <PortfolioName name={book?.name} includeColorChip color={getPortfolioColor(book?.id)} />
        </Box>
      </Box>
    ),
    [book?.id, book?.name, isConfirmation],
  );

  const dialogActions = useMemo(
    () => (
      <>
        <Button variant="text" onClick={onCloseHandler} sx={{ fontWeight: 600, textTransform: "capitalize" }}>
          Cancel
        </Button>

        <Maybe condition={!isConfirmation}>
          <ActionButton
            type="submit"
            form="edit-book-price-form"
            style={{ fontWeight: 500, width: "190px", textTransform: "capitalize" }}
            endIcon={<ArrowForwardIosIcon sx={{ height: "14px" }} />}
          >
            Next: Confirmation
          </ActionButton>
        </Maybe>

        <Maybe condition={isConfirmation}>
          <Stack direction="row" sx={{ marginRight: "-10px" }} gap={1}>
            <ActionButton
              style={{
                fontWeight: 500,
                width: "160px",
                textTransform: "capitalize",
                backgroundColor: "rgba(255, 255, 255, 1)",
                color: "rgba(30, 70, 57, 1)",
                "&:hover": {
                  backgroundColor: "rgba(255, 255, 255, 1)",
                  boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
                },
                boxShadow: "rgb(0 0 0 / 10%) 0px 4px 4px",
              }}
              startIcon={<ArrowBackIosIcon sx={{ height: "14px" }} />}
              onClickHandler={() => setIsConfirmation(false)}
            >
              Back: Set Price
            </ActionButton>
            <ActionButton
              style={{ fontWeight: 500, width: "140px", textTransform: "capitalize" }}
              onClickHandler={submitBasketPrice}
            >
              Save New Price
            </ActionButton>
          </Stack>
        </Maybe>
      </>
    ),
    [isConfirmation, onCloseHandler, submitBasketPrice],
  );

  return (
    <Dialog open={isOpen} onClose={onCloseHandler}>
      <DialogContent sx={{ height: isConfirmation ? "310px" : "660px", overflowY: "hidden" }}>
        {dialogHead}
        <Box mt={4}>
          <Maybe condition={!isConfirmation}>
            <form id="edit-book-price-form" onSubmit={onSubmitHandler}>
              <fieldset style={{ display: "contents" }}>
                <Box className={classes.stepsBox} mb={2}>
                  <Maybe condition={!!steps && steps.length > 0}>
                    <Button disabled={true} className={classes[getStepClass(steps[0]?.status)]}>
                      {steps[0].status === StepsStatus.DONE ? (
                        <CheckIcon sx={{ padding: "1px", height: "15px" }} />
                      ) : (
                        steps[0].name
                      )}
                    </Button>
                    <Typography component="div" className={classes.stepTitle}>
                      {steps[0]?.label ?? ""}
                    </Typography>
                  </Maybe>
                </Box>

                <NumericFormat
                  sx={{ width: "100%", marginTop: "10px" }}
                  required
                  value={newBasketPrice}
                  defaultValue={0}
                  decimalScale={2}
                  fixedDecimalScale
                  prefix="$"
                  inputProps={{ maxLength: 8 }}
                  allowNegative={false}
                  label="Price"
                  customInput={TextField}
                  type="text"
                  thousandSeparator={","}
                  onChange={onBasketPriceChange}
                  error={!isValidBasketPrice()}
                  helperText={getBasketPriceHelperText()}
                />

                <Box className={classes.stepsBox} mt={4} mb={2}>
                  <Maybe condition={!!steps && steps.length > 0}>
                    <Button disabled={true} className={classes[getStepClass(steps[1]?.status)]}>
                      {steps[1].status === StepsStatus.DONE ? (
                        <CheckIcon sx={{ padding: "1px", height: "15px" }} />
                      ) : (
                        steps[1].name
                      )}
                    </Button>
                    <Typography component="div" className={classes.stepTitle}>
                      {steps[1]?.label ?? ""}
                    </Typography>
                  </Maybe>
                </Box>

                <Stack direction="row" gap="20px">
                  <NumericFormat
                    sx={{ width: "100%", marginTop: "10px" }}
                    required={isEmpty(riskDollarAmount?.value)}
                    value={riskPremiumPercent?.value}
                    name="percentageAmount"
                    defaultValue={0}
                    decimalScale={2}
                    fixedDecimalScale
                    suffix="%"
                    inputProps={{ maxLength: 8 }}
                    allowNegative={false}
                    label="Percentage"
                    customInput={TextField}
                    type="text"
                    thousandSeparator={","}
                    onChange={onPercentageAmountChanged}
                    error={riskPremiumPercent.error}
                    helperText={riskPremiumPercent.message}
                  />

                  <NumericFormat
                    sx={{ width: "100%", marginTop: "10px" }}
                    value={riskDollarAmount?.value}
                    required={isEmpty(riskPremiumPercent?.value)}
                    name="dollarAmount"
                    defaultValue={0}
                    decimalScale={2}
                    fixedDecimalScale
                    prefix="$"
                    inputProps={{ maxLength: 8 }}
                    allowNegative={false}
                    label="Dollar amount"
                    customInput={TextField}
                    type="text"
                    thousandSeparator={","}
                    onChange={onDollarAmountChanged}
                    error={riskDollarAmount.error}
                    helperText={riskDollarAmount.message}
                  />
                </Stack>

                <Box className={classes.stepsBox} mt={4} mb={2}>
                  <Maybe condition={!!steps && steps.length > 0}>
                    <Button disabled={true} className={classes[getStepClass(steps[2]?.status)]}>
                      {steps[2].status === StepsStatus.DONE ? (
                        <CheckIcon sx={{ padding: "1px", height: "15px" }} />
                      ) : (
                        steps[2].name
                      )}
                    </Button>
                    <Typography component="div" className={classes.stepTitle}>
                      {steps[2]?.label ?? ""}
                    </Typography>
                  </Maybe>
                </Box>

                {algoHeader}

                <Box mt={2} width="100%">
                  <SummaryGroup group={customerPortalPriceGroup} />
                </Box>
              </fieldset>
            </form>
          </Maybe>
          <Maybe condition={isConfirmation}>
            <Box mt={4}>{algoHeader}</Box>
            <Box mt={1} width="100%">
              <SummaryGroup group={confirmationPriceGroup} />
            </Box>
          </Maybe>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          backgroundColor: "rgba(250, 250, 250, 1)",
          paddingRight: "24px",
          justifyContent: "space-between",
          border: "none",
        }}
      >
        {dialogActions}
      </DialogActions>
    </Dialog>
  );
}
