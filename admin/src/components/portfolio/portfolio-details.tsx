import React, { useState, useEffect, Fragment } from "react";
import useS<PERSON> from "swr";
import Holdings from "./holdings";
import TransactionsCharts from "./transactions-charts";
import {
  TransactionQueryResponse,
  BookType,
  AdminBookResponse,
  AdminProjectQueryResponse,
  AdminBookQueryResponse,
  BookRelations,
  OrderByDirection,
  TransactionQuery,
  uuid,
  TransactionOrderByOptions,
  TransactionType,
  RetirementStatus,
  PurchaseStatus,
  ProjectRelations,
  GroupedAllocationWithNestedResponse,
} from "@rubiconcarbon/shared-types";
import Maybe from "../maybe/maybe";
import BookSelector from "./book-selector";
import { Box, Divider, IconButton, Stack, Tooltip, Typography } from "@mui/material";
import BookSummary from "./book-summary";
import EligibleCreditTypes from "./eligible-credit-types";
import { CheckStatus } from "../ui/check-staus/check-status";
import { InfoRounded, MoreHorizRounded } from "@mui/icons-material";
import GenericDialog from "../ui/generic-dialog/generic-dialog";
import ProjectValidation from "../alerts/project-validation/project-validation";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { useLogger } from "@/providers/logging";

import classes from "./styles/portfolio-details.module.scss";

const basketSumaryStyle = {
  boxShadow: "0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)",
};

interface PortfolioDetailsProps {
  bookId: string;
}

export default function PortfolioDetails(props: PortfolioDetailsProps): JSX.Element {
  const { bookId } = props;
  const [allowedProjectTypes, setAllowedProjectTypes] = useState<number[]>();
  const [allowedOrganizations, setAllowedOrganizations] = useState<string[]>();
  const [showMoreOrgs, setShowMoreOrgs] = useState<boolean>(false);
  const [bookType, setBookType] = useState<BookType>();
  const { enqueueError } = useSnackbarVariants();
  const { logger } = useLogger();

  const {
    data: bookResponse,
    isLoading: isBookLoading,
    error: bookError,
    mutate: refreshBook,
  } = useRequest<AdminBookResponse>({
    url: `admin/books/${bookId}`,
    queryParams: {
      includeRelations: [
        BookRelations.OWNER_ALLOCATIONS_NESTED,
        BookRelations.OWNER_ALLOCATIONS_BY_PROJECT_TYPE,
        BookRelations.ORGANIZATION,
        BookRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
        BookRelations.ASSET_ALLOCATIONS,
      ],
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to load portfolio.");
        logger.error(`Unable to load portfolio: ${error?.message}.`, {});
      },
    },
  });

  const { data: bookQueryResponse, mutate: refreshBooks } = useSWR<AdminBookQueryResponse>("/admin/books?limit=500");

  // todo : @kofi why do we need this call?
  const {
    data: availableProjects,
    isLoading: loadingAvailProj,
    error: availProjError,
  } = useSWR<AdminProjectQueryResponse>(
    allowedProjectTypes
      ? `/admin/projects?projectTypeIds=${allowedProjectTypes}&includeRelations=${ProjectRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE}`
      : null,
  );

  const {
    data: bookTransactions,
    isLoading: loadingBookTrans,
    error: basketTransError,
  } = useRequest<TransactionQueryResponse, object, object, TransactionQuery>({
    url: "/admin/transactions",
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      assetIds: [bookId as uuid],
      types: [TransactionType.PURCHASE, TransactionType.RETIREMENT],
      orderBys: [`${TransactionOrderByOptions.CREATED_AT}:${OrderByDirection.ASC_NULLS_LAST}`],
      statuses: [PurchaseStatus.SETTLED, RetirementStatus.COMPLETED],
    },
  });

  useEffect(() => {
    if (!!bookResponse) {
      setBookType(bookResponse.type);
      setAllowedOrganizations([bookResponse.organization?.name].filter((name) => !!name));
      if (bookResponse.ownerAllocationsByProjectType) {
        setAllowedProjectTypes(bookResponse.ownerAllocationsByProjectType.map((e) => e.projectType.id));
      }
    }
  }, [bookResponse]);

  const reloadData = (): void => {
    refreshBook();
    refreshBooks();
  };

  return (
    <>
      <ProjectValidation
        extendedAllocations={
          ((bookResponse?.ownerAllocations as GroupedAllocationWithNestedResponse)?.allocations as any) || []
        }
      />
      <Stack direction="row" mt={4} gap={1}>
        <BookSelector bookId={bookId} bookType={bookType} bookQueryResponse={bookQueryResponse} />
        <Maybe condition={bookType === BookType.PORTFOLIO_CUSTOM}>
          <Divider sx={{ height: 52, m: 0.5 }} orientation="vertical" />
          <Stack alignItems="center" direction="row" gap={1}>
            <>
              {allowedOrganizations?.slice(0, 2)?.map((name, index) => (
                <Typography key={name}>
                  {name}
                  {index >= 0 && index !== allowedOrganizations?.slice(0, 2)?.length - 1 ? "," : ""}
                </Typography>
              ))}
              <Maybe condition={!!allowedOrganizations?.slice(2)?.length}>
                <IconButton color="primary" onClick={() => setShowMoreOrgs(true)}>
                  <MoreHorizRounded />
                </IconButton>
              </Maybe>
            </>
            <Tooltip title={`Allowed organization${allowedOrganizations?.length > 1 ? "s" : ""} for portfolio`}>
              <InfoRounded />
            </Tooltip>
          </Stack>
        </Maybe>
      </Stack>
      <CheckStatus
        data={[bookResponse, availableProjects, bookTransactions]}
        isLoading={[isBookLoading, loadingBookTrans, loadingAvailProj]}
        error={[bookError, basketTransError, availProjError]}
      >
        <Maybe condition={!!bookResponse}>
          <Box mt={2}>
            <EligibleCreditTypes book={bookResponse} refresh={refreshBook} />
          </Box>
          <Box mt={4}>
            <Typography
              variant="body2"
              component="h4"
              sx={{
                fontSize: "20px",
                fontWeight: "500",
              }}
            >
              Overview of portfolio and inventory
            </Typography>
          </Box>
          <Box mt={3} sx={basketSumaryStyle}>
            <BookSummary book={bookResponse} refresh={refreshBook} reloadData={reloadData} />
          </Box>
        </Maybe>
        <Maybe condition={!!bookResponse && !!availableProjects?.data}>
          <Holdings book={bookResponse} availableProjects={availableProjects?.data} />
        </Maybe>
        <Maybe condition={!loadingBookTrans && bookTransactions?.data?.length > 0}>
          <TransactionsCharts basketTransactions={bookTransactions} />
        </Maybe>
        <GenericDialog
          open={showMoreOrgs}
          title={`Allowed organization${allowedOrganizations?.length > 1 ? "s" : ""} for portfolio`}
          onClose={() => setShowMoreOrgs(false)}
          classes={{
            root: classes.AllowedOrgsDialog,
            content: classes.Content,
          }}
        >
          <Stack gap={1}>
            {allowedOrganizations?.map((name) => (
              <Fragment key={name}>
                <Typography>{name}</Typography>
                <Divider />
              </Fragment>
            ))}
          </Stack>
        </GenericDialog>
      </CheckStatus>
    </>
  );
}
