import Page from "@/components/layout/containers/page";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { Authorize } from "@/services/authorize";
import NotificationSettings from "@/components/notification-settings/notification-settings";

export default function NotificationSettingsPage(): JSX.Element {
  return (
    <Authorize permissions={[PermissionEnum.LOGIN]}>
      <Page>
        <NotificationSettings />
      </Page>
    </Authorize>
  );
}
