.SubHeader {
    color: black;
    font-weight: bold;
}

.Table {
    border-radius: 2px;
    border: solid #E0E0E0 1px;

    .Header {
        background-color: #F5F5F5;
        padding: 10px;
        
        .HeaderCell {
            color: black;

            p {
                font-weight: 600;
            }
        }
    }

    .Body {
        padding: 10px;

        .BodyRow {
            min-height: 70px;

            .InputRoot {
                min-height: 52px;
            
                .SelectIcon {
                    margin-right: 15px;
                }
            }

            .Autocomplete {
                label {
                    line-height: 1.4em;
                    height: 20px;

                    &[data-shrink="true"] {
                        line-height: 1.4em;
                    }
                }

                .InputRoot {
                    min-height: 52px;
                }   
            }
        }

        .Alert {
            display: flex;
            align-items: center;
            border: unset;
            border-radius: 5px;
            background-color: #E5F6FD;
            margin-bottom: 8px;
            padding: 4px 10px;

            .Icon {
                color: #1195D6;
                font-size: x-large;
            }

            .Message {
                color: #014361;
            }

            &.<PERSON>rror {
               background-color: #fde5e5;

               .Icon {
                    color: #D61138;
                }

               .Message {
                    color: #611901;
                }
            }
        }
    }

    .Footer {
        padding: 10px 0;

        .FooterRow {
            color: black;

            .FooterCell {
                padding-right: 5px;
                .Label {
                    font-weight: 600;
                    text-wrap: wrap;
                }

                .Value {
                    font-weight: 300;
                    text-align: right;
                }
            }
        }
    }  
}

.CommentButton {
    border-radius: 5px;
    height: 34px;
}