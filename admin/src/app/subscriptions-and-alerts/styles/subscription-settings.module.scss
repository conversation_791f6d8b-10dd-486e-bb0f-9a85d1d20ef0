.Container {
    .Content {
        border-radius: 10px;
        color: black;

        .SectionHeader {
            .HeaderLabel {
                color: #383838;
                font-size: medium;
                font-weight: bold;
            }

            .DescriptionLabel {
                color: #767676;
                font-size: medium;
                font-weight: lighter
            }
            
            .SubLabel {
                color: #767676;
                font-size: small;
                font-weight: lighter
            }
        }

        .Frequency {
            min-width: 300px;
        }

        @media (max-width: 1360px){
            .Frequency {
                min-width: 100%;
            }
        }

        .Actions {
            position: sticky;
            bottom: 0;
            height: 50px;
            background-color: rgb(250, 250, 250);

            .TextButton {
                font-weight: 600;
            }
        }
    }
}
