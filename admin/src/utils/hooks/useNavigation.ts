import { useAppPathname, useAppRouter, createUrl } from "@/utils/hooks/router-hooks";

type NavigationParams = Record<string, string | string[] | number | null | undefined>;

type UseNavigationReturn = {
  /**
   * Current path (without search params)
   */
  currentPath: string;

  /**
   * Extends the current path with additional segments
   */
  extendPathWithSegments: (segments: string) => string;

  /**
   * Extends the current path with additional segments and search params
   */
  extendPathWithSegmentsAndParams: (segments: string, params?: NavigationParams) => string;

  /**
   * Reduces the current path by removing a specified number of segments
   */
  reducePathBySegmentCount: (segmentCount: number) => string;

  /**
   * Navigates to an extended path (current path + new segments)
   */
  pushToPath: (segments: string, params?: NavigationParams) => boolean;

  /**
   * Navigates back by removing segments from the current path
   */
  popFromPath: (segmentCount: number, params?: NavigationParams) => boolean;

  /**
   * Replaces segments at the end of the current path with new segments
   */
  replacePathFromSegment: (segmentCount: number, segments: string, params?: NavigationParams) => boolean;
};

/**
 * Hook for navigation operations in Next.js App Router
 */
const useNavigation = (): UseNavigationReturn => {
  const pathname = useAppPathname();
  const router = useAppRouter();

  /**
   * Sanitizes a path to ensure proper format
   */
  const sanitizePath = (path: string): string => {
    // Replace multiple consecutive slashes with a single slash
    return path.replace(/\/+/g, "/");
  };

  const extendPathWithSegments = (segments: string): string => {
    return sanitizePath(`${pathname}/${segments}`);
  };

  const extendPathWithSegmentsAndParams = (segments: string, params?: NavigationParams): string => {
    const path = extendPathWithSegments(segments);
    return params ? createUrl(path, params) : path;
  };

  const reducePathBySegmentCount = (segmentCount: number): string => {
    if (!pathname || segmentCount <= 0) return pathname || "/";

    const segments = pathname.split("/").filter(Boolean);

    // Remove segments from the end
    const remainingSegments = segments.slice(0, Math.max(0, segments.length - segmentCount));

    // If no segments left, return root path
    if (remainingSegments.length === 0) return "/";

    // Join remaining segments with slashes
    return "/" + remainingSegments.join("/");
  };

  const pushToPath = (segments: string, params?: NavigationParams): boolean => {
    try {
      const targetPath = params
        ? createUrl(extendPathWithSegments(segments), params)
        : extendPathWithSegments(segments);

      router.push(targetPath);
      return true;
    } catch (error) {
      console.error("Navigation error:", error);
      return false;
    }
  };

  const popFromPath = (segmentCount: number, params?: NavigationParams): boolean => {
    try {
      const targetPath = params
        ? createUrl(reducePathBySegmentCount(segmentCount), params)
        : reducePathBySegmentCount(segmentCount);

      router.push(targetPath);
      return true;
    } catch (error) {
      console.error("Navigation error:", error);
      return false;
    }
  };

  const replacePathFromSegment = (segmentCount: number, segments: string, params?: NavigationParams): boolean => {
    try {
      const basePath = reducePathBySegmentCount(segmentCount);
      const newPath = sanitizePath(`${basePath}/${segments}`);

      const targetPath = params ? createUrl(newPath, params) : newPath;

      router.push(targetPath);
      return true;
    } catch (error) {
      console.error("Navigation error:", error);
      return false;
    }
  };

  return {
    currentPath: pathname,
    extendPathWithSegments,
    extendPathWithSegmentsAndParams,
    reducePathBySegmentCount,
    pushToPath,
    popFromPath,
    replacePathFromSegment,
  };
};

export default useNavigation;
