import { uuid } from "@rubiconcarbon/shared-types";
import CustomButton from "@components/ui/custom-button/custom-button";
import COLORS from "@components/ui/theme/colors";
import Box from "@mui/material/Box";
import { useCallback, useContext, useMemo, useState } from "react";
import { AxiosContext } from "@providers/axios-provider";
import useSWR from "swr";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import MaterialReactTable, { MRT_Cell, MRT_ColumnDef } from "material-react-table";
import Typography from "@mui/material/Typography";
import { Remark } from "react-remark";
import CircularProgress from "@mui/material/CircularProgress";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";
import NotInterestedIcon from "@mui/icons-material/NotInterested";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogActions from "@mui/material/DialogActions";
import Button from "@mui/material/Button";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import InfoIcon from "@mui/icons-material/Info";
import { Stack, TextField, Tooltip } from "@mui/material";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import { isEmpty } from "lodash";

import classes from "../styles/data-management.module.scss";

const MISSING_MEMO_MSG = "A memo is required to approve changes.";

export interface Metadata {
  asOf?: Date;
  now?: Date;
}

export interface Data<T> {
  data: T;
  metadata: Metadata;
}

interface DifferencesReport {
  projectId: uuid;
  registryProjectId: string;
  field: string;
  value: any;
  newValue: any;
}

function requestFieldName(s: string): string {
  const val = s.toLowerCase().replace(/([-_][a-z])/g, (group) => group.toUpperCase().replace("_", ""));
  return val;
}

function FormattedCell(cell: MRT_Cell<DifferencesReport>): JSX.Element {
  if (cell.getValue() === null) {
    return <NotInterestedIcon sx={{ color: "rgb(0,0,0,.1)" }}></NotInterestedIcon>;
  }

  if (cell.getValue() === false) {
    return <CloseIcon></CloseIcon>;
  }

  if (cell.getValue() === true) {
    return <CheckIcon></CheckIcon>;
  }

  if (cell.row.original.field === "min_percentage" || cell.row.original.field === "max_percentage")
    return (
      <div className={classes.values}>
        <Remark>{(cell.getValue<number>() * 100).toFixed(2) + "%"}</Remark>
      </div>
    );

  if (cell.row.original.field === "min_quantity" || cell.row.original.field === "max_quantity")
    return (
      <div className={classes.values}>
        <Remark>{cell.getValue<number>().toFixed(0)}</Remark>
      </div>
    );

  const numericFields = [
    "additionality_score",
    "certification_score",
    "climate_impact",
    "climate_impact_risk_adjusted",
    "durability_score",
    "integrity_grade_score",
    "integrity_grade_score_risk_adjusted",
    "future_delivery_risk",
  ];

  if (numericFields.includes(cell.row.original.field)) {
    return (
      <div className={classes.values}>
        <Remark>{cell.getValue<number>().toString()}</Remark>
      </div>
    );
  }

  return (
    <div className={classes.values}>
      <Remark>{cell.getValue<string>()}</Remark>
    </div>
  );
}

export function ProjectsDiffs({ cancel, endpoint }: { cancel: () => void; endpoint: string }): JSX.Element {
  const { api, reportingFetcher } = useContext(AxiosContext);
  const [memo, setMemo] = useState<string>();
  const { enqueueError } = useSnackbarVariants();

  const {
    data: response,
    error,
    isLoading,
    isValidating,
  } = useSWR<Data<any>>(endpoint.replace(/^reporting\//, ""), {
    fetcher: reportingFetcher as any,
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    keepPreviousData: false,
  });
  const { logger } = useLogger();
  const allowApproveChanges: boolean = useMemo(() => !isEmpty(memo), [memo]);

  const memoHandler = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setMemo(event.target.value);
  }, []);

  const syncProjects = useCallback(async () => {
    const requestData = response?.data?.differences?.map((x: any) => {
      const result: { [key: string]: any } = { id: x.project_id };
      Object.keys(x.diffs).forEach((k) => (result[requestFieldName(k)] = x.diffs[k].new));

      if ("sdgs" in result) {
        result["sdgIds"] = result["sdgs"] == null ? [] : (result["sdgs"] || "").split(",").map((x: any) => Number(x));
        delete result.sdgs;
      }

      // remove the project type name -- we have ids
      if ("projectType" in result) {
        delete result.projectType;
      }

      // rename some fields
      if ("projectName" in result) {
        result["name"] = result["projectName"];
        delete result.projectName;
      }
      if ("durabilityBlurb" in result) {
        result["permanenceBlurb"] = result["durabilityBlurb"];
        delete result.projectName;
      }
      if ("projectCountry" in result) {
        result["countryCode"] = result["projectCountry"];
        delete result.projectName;
      }

      // eligibilities
      for (const x of [
        "eligibilityAccreditationsWaterOrg",
        "eligibilityAccreditationsVerraCcb",
        "eligibilityAccreditationsVerraSdvista",
        "eligibilityAccreditationsCorsiaPhase1",
        "eligibilityAccreditationsCorsiaPhasePilot",
        "eligibilityAccreditationsIcvcmCcpLabeled",
      ]) {
        delete result[x];
      }

      return {
        ...result,
        memo,
      };
    });

    await api
      .patch<any>("admin/projects", requestData, {})
      .then(() => {
        setMemo("");
        cancel();
      })
      .catch((e) => {
        logger.error(`Unable to sync projects: ${e?.response?.data?.message}`, {});
        enqueueError(`Unable to sync projects: ${e?.response?.data?.message}`);
      });
  }, [response, api, cancel, enqueueError, logger, memo]);

  const columns = useMemo<MRT_ColumnDef<DifferencesReport>[]>(
    () => [
      { accessorKey: "projectId", header: "ProjectID", maxSize: 200, size: 180 },
      { accessorKey: "registryProjectId", header: "Project", maxSize: 200, size: 180 },
      { accessorKey: "field", header: "Field", maxSize: 200, size: 180 },
      {
        accessorKey: "value",
        header: "Current Value",
        maxSize: 200,
        size: 180,
        Cell: ({ cell }): JSX.Element => FormattedCell(cell),
      },
      {
        accessorKey: "newValue",
        header: "New Value",
        maxSize: 200,
        size: 180,
        Cell: ({ cell }): JSX.Element => FormattedCell(cell),
      },
    ],
    [],
  );

  const errorColumns = useMemo<MRT_ColumnDef<{ field: string; projects: string[] }>[]>(
    () => [
      { accessorKey: "field", header: "Field" },
      {
        accessorKey: "projects",
        header: "Projects",
        Cell: ({ cell }): JSX.Element => {
          return (
            <div style={{ display: "flex", flexDirection: "column" }}>
              {cell.getValue<string[]>().map((x) => (
                <div key={x}>{x}</div>
              ))}
            </div>
          );
        },
      },
    ],
    [],
  );

  if (isLoading || isValidating) {
    return (
      <div style={{ display: "flex", justifyContent: "center" }}>
        <CircularProgress></CircularProgress>
      </div>
    );
  }

  if (error !== undefined) {
    return (
      <>
        <div style={{ display: "flex", justifyContent: "center", height: "100px", color: "red", alignItems: "center" }}>
          An error has occurred accessing the Google Spreadsheet file.
        </div>
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <CustomButton
            onClickHandler={cancel}
            style={{
              borderRadius: 2,
              backgroundColor: COLORS.rubiconGreenLight,
              fontWeight: 600,
              color: COLORS.paleBlack,
              marginRight: "20px",
              height: "35px",
              "&:hover": {
                backgroundColor: COLORS.whiteGrey,
              },
              marginBottom: "10px",
            }}
          >
            Cancel
          </CustomButton>
        </div>
      </>
    );
  }

  if (response?.data?.errors != null) {
    const errors = Object.keys(response.data.errors).map((k) => {
      return { field: k, projects: response.data.errors[k] };
    });

    return (
      <>
        <Typography variant="button" style={{ marginLeft: "20px", marginTop: "20px", color: "red" }}>
          Errors:
        </Typography>
        <div>
          <MaterialReactTable
            columns={errorColumns}
            data={errors}
            enableColumnActions={false}
            enableColumnFilters={false}
            enablePagination={true}
            enableSorting={false}
            enableBottomToolbar={true}
            enableTopToolbar={false}
            muiTableContainerProps={{ sx: { boxShadow: "none", maxHeight: "600px", minHeight: "300px" } }}
            muiTableProps={{
              sx: {
                padding: "10px",
                boxShadow: "none",
                border: "0px",
                width: "100%",
              },
            }}
            muiTableHeadRowProps={{ sx: { boxShadow: "none", border: "0px" } }}
            muiTablePaperProps={{
              sx: {
                boxShadow: "none",
                width: "100%",
                height: "100%",
                padding: "0 5px",

                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
              },
            }}
            initialState={{
              density: "compact",
              pagination: { pageSize: 100, pageIndex: 0 },
            }}
            enableColumnDragging={false}
            enableStickyHeader
            enableStickyFooter
            enableColumnResizing={false}
            layoutMode={"grid"}
          />
        </div>
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <CustomButton
            onClickHandler={cancel}
            style={{
              borderRadius: 2,
              backgroundColor: COLORS.rubiconGreenLight,
              fontWeight: 600,
              color: COLORS.paleBlack,
              marginRight: "20px",
              height: "35px",
              "&:hover": {
                backgroundColor: COLORS.whiteGrey,
              },
              marginBottom: "10px",
            }}
          >
            Cancel
          </CustomButton>
        </div>
      </>
    );
  }

  const data: any[] = response?.data?.differences
    .map((d: any) =>
      Object.keys(d.diffs)
        .map((k) => ({
          projectId: d.project_id,
          registryProjectId: d.registry_project_id,
          field: k,
          value: d.diffs[k].db,
          newValue: d.diffs[k].new,
        }))
        .filter((x) => x.field != "project_type_id" && x.field != "eligibility_accreditations"),
    )
    .flat();

  data.push(
    ...(response?.data?.new?.new ?? []).map((x: string) => ({
      projectId: x,
      registryProjectId: x,
      field: (
        <div style={{ color: "red", display: "flex", alignItems: "center" }}>
          New Project
          <Tooltip title="New Projects are not automatically synced. Please add it directly on the platform before performing a sync operation.">
            <InfoIcon style={{ fontSize: "18px", marginLeft: "4px" }} />
          </Tooltip>
        </div>
      ),
      value: undefined as any,
      newValue: undefined as any,
    })),
  );

  data.push(
    ...(response?.data?.new?.db ?? []).map((x: string) => ({
      projectId: x,
      registryProjectId: x,
      field: (
        <div style={{ color: "red", display: "flex", alignItems: "center" }}>
          Project not in the Data Sync Sheet
          <Tooltip title="Please run the 'Update Google Sheets' operation to add the project to the data sync sheet.">
            <InfoIcon style={{ fontSize: "18px", marginLeft: "4px" }} />
          </Tooltip>
        </div>
      ),
      value: undefined as any,
      newValue: undefined as any,
    })),
  );

  return (
    <>
      <div style={{ display: "flex", justifyContent: "flex-end" }}>
        <Maybe condition={data?.length > 0}>
          <CustomButton
            onClickHandler={syncProjects}
            tooltip={!allowApproveChanges ? MISSING_MEMO_MSG : ""}
            isDisabled={!allowApproveChanges}
            style={{
              borderRadius: 2,
              backgroundColor: COLORS.rubiconGreenLight,
              fontWeight: 600,
              color: COLORS.paleBlack,
              marginRight: "20px",
              height: "35px",
              "&:hover": {
                backgroundColor: COLORS.whiteGrey,
              },
              marginBottom: "10px",
            }}
          >
            Approve Changes
          </CustomButton>
          <CustomButton
            onClickHandler={cancel}
            style={{
              borderRadius: 2,
              backgroundColor: COLORS.rubiconGreenLight,
              fontWeight: 600,
              color: COLORS.paleBlack,
              marginRight: "20px",
              height: "35px",
              "&:hover": {
                backgroundColor: COLORS.whiteGrey,
              },
              marginBottom: "10px",
            }}
          >
            Cancel
          </CustomButton>
        </Maybe>
      </div>
      <Stack mb={2} gap={1} sx={{ padding: "10px" }}>
        <Typography variant="body2" component="h4" sx={{ fontSize: "14px", fontWeight: 700 }}>
          {`Memo`}
        </Typography>
        <Typography variant="body2" component="h5" sx={{ fontSize: "12px" }}>
          {`Please provide any additional details or context to clarify the changes made.`}
        </Typography>
        <Box mt={1}>
          <TextField label="Memo" value={memo ?? ""} onChange={memoHandler} required fullWidth multiline />
        </Box>
      </Stack>

      <MaterialReactTable
        columns={columns as MRT_ColumnDef<any>[]}
        data={data}
        renderEmptyRowsFallback={() => (
          <Typography
            sx={{
              color: "text.secondary",
              fontStyle: "italic",
              py: "2rem",
              textAlign: "center",
              width: "100%",
            }}
          >
            No Data Changes.
          </Typography>
        )}
        enableColumnActions={false}
        enableColumnFilters={false}
        enablePagination={true}
        enableSorting={false}
        enableBottomToolbar={true}
        enableTopToolbar={false}
        muiTableContainerProps={{ sx: { boxShadow: "none", maxHeight: "600px" } }}
        muiTableProps={{
          sx: {
            padding: "10px",
            boxShadow: "none",
            border: "0px",
            width: "100%",
          },
        }}
        muiTableHeadRowProps={{ sx: { boxShadow: "none", border: "0px" } }}
        muiTablePaperProps={{
          sx: {
            boxShadow: "none",
            width: "100%",
            height: "100%",
            padding: "0 5px",

            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
          },
        }}
        initialState={{
          density: "compact",
          grouping: ["registryProjectId"],
          expanded: true,
          columnVisibility: { projectId: false },
          pagination: { pageSize: 100, pageIndex: 0 },
          sorting: [
            { id: "registryProjectId", desc: false },
            { id: "field", desc: false },
          ],
        }}
        enableGrouping
        enableColumnDragging={false}
        enableStickyHeader
        enableStickyFooter
        enableColumnResizing={false}
        layoutMode={"grid"}
      />

      <div style={{ display: "flex", justifyContent: "flex-end", marginTop: "10px" }}>
        <Maybe condition={data?.length > 0}>
          <CustomButton
            onClickHandler={syncProjects}
            isDisabled={!allowApproveChanges}
            tooltip={!allowApproveChanges ? MISSING_MEMO_MSG : ""}
            style={{
              borderRadius: 2,
              backgroundColor: COLORS.rubiconGreenLight,
              fontWeight: 600,
              color: COLORS.paleBlack,
              marginRight: "20px",
              height: "35px",
              "&:hover": {
                backgroundColor: COLORS.whiteGrey,
              },
              marginBottom: "10px",
            }}
          >
            Approve Changes
          </CustomButton>
        </Maybe>
        <CustomButton
          onClickHandler={cancel}
          style={{
            borderRadius: 2,
            backgroundColor: COLORS.rubiconGreenLight,
            fontWeight: 600,
            color: COLORS.paleBlack,
            marginRight: "20px",
            height: "35px",
            "&:hover": {
              backgroundColor: COLORS.whiteGrey,
            },
            marginBottom: "10px",
          }}
        >
          {data?.length > 0 ? "Cancel" : "Close"}
        </CustomButton>
      </div>
    </>
  );
}

export default function SyncWidget(props: {
  endpoint: string;
  title: string;
  spreadsheetId: string;
  spreadsheetName: string;
}): JSX.Element {
  const { api } = useContext(AxiosContext);
  const [isSyncProjects, setIsSyncProjects] = useState(false);
  const [openAlertUpdateProjects, setOpenAlertUpdateProjects] = useState(false);
  const [openUpdatedProjects, setOpenUpdatedProjects] = useState(false);

  const handleAlertUpdateProjectsClose = (): void => {
    setOpenAlertUpdateProjects(false);
  };

  const handleUpdateGoogleSheets = useCallback(async () => {
    await api.post(props.endpoint);

    setOpenUpdatedProjects(true);
  }, [api, props.endpoint]);

  const updateGoogleSheets = useCallback(async () => {
    const data = (await api.get(props.endpoint))?.data;

    if (data.differences.length > 0 || data.new.new.length > 0) {
      setOpenAlertUpdateProjects(true);
    } else {
      await handleUpdateGoogleSheets();
    }
  }, [api, handleUpdateGoogleSheets, props.endpoint]);

  return (
    <Box className={classes.container}>
      <Typography variant="h6" sx={{ marginLeft: "20px", marginBottom: "10px" }}>
        {props.title}
      </Typography>

      <Maybe condition={!isSyncProjects}>
        <div style={{ margin: "20px" }}>
          <table>
            <tbody>
              <tr>
                <th>Spreadsheet:</th>
                <td>
                  <a
                    style={{ textDecoration: "none", color: "WindowText", display: "flex" }}
                    href={`https://docs.google.com/spreadsheets/d/${props.spreadsheetId}/edit#gid=0`}
                    target="_blank"
                  >
                    {props.spreadsheetName}
                    <OpenInNewIcon style={{ marginLeft: "4px", fontSize: "14px" }} />
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <CustomButton
          onClickHandler={() => {
            setIsSyncProjects(true);
          }}
          style={{
            borderRadius: 2,
            backgroundColor: COLORS.rubiconGreenLight,
            fontWeight: 600,
            color: COLORS.paleBlack,
            marginLeft: "20px",
            height: "35px",
            "&:hover": {
              backgroundColor: COLORS.whiteGrey,
            },
          }}
        >
          Sync from Google Sheets
        </CustomButton>

        <CustomButton
          onClickHandler={updateGoogleSheets}
          style={{
            borderRadius: 2,
            backgroundColor: COLORS.rubiconGreenLight,
            fontWeight: 600,
            color: COLORS.paleBlack,
            marginLeft: "20px",
            height: "35px",
            "&:hover": {
              backgroundColor: COLORS.whiteGrey,
            },
          }}
        >
          Update Google Sheets
        </CustomButton>

        <Dialog
          open={openAlertUpdateProjects}
          onClose={handleAlertUpdateProjectsClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title">{"Unsaved changes"}</DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-description">
              The Project Data Google Sheet contains data that is not yet synced to the internal platform. Do you want
              to overwrite it with the internal data?
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              color="error"
              onClick={async () => {
                await handleUpdateGoogleSheets();
                handleAlertUpdateProjectsClose();
              }}
            >
              Overwrite
            </Button>
            <Button onClick={handleAlertUpdateProjectsClose} autoFocus>
              Cancel
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog
          open={openUpdatedProjects}
          onClose={() => setOpenUpdatedProjects(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title">{"Data Sheets Updated"}</DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-description">
              The Projects Data Google Sheet was successfully updated.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenUpdatedProjects(false)} autoFocus>
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </Maybe>

      <div>
        <Maybe condition={isSyncProjects}>
          <Typography variant="body2" sx={{ marginLeft: "20px" }}>
            Sync Projects from Google Sheets to the internal platform.
          </Typography>
          <ProjectsDiffs cancel={() => setIsSyncProjects(false)} endpoint={props.endpoint}></ProjectsDiffs>
        </Maybe>
      </div>
    </Box>
  );
}
