import React, { useState, useEffect, useMemo } from "react";
import {
  RctAssetResponse,
  AdminRetirementQuery,
  RetirementRelations,
  RetirementResponse,
} from "@rubiconcarbon/shared-types";
import { CheckStatus } from "../../ui/check-staus/check-status";
import RetirementCompositionTable from "./retirement-composition-table";
import { TransactionProjectVintage, mapTransactionProjectVintageData } from "@/models/transaction-project-vintage";
import { useRequest } from "@rubiconcarbon/frontend-shared";

export default function RetirementComposition(props: { retirementId: string | string[] }): JSX.Element {
  const { retirementId } = props;
  const [projectVintages, setProjectVintages] = useState<TransactionProjectVintage[]>();
  const {
    data: retirement,
    error,
    isLoading,
    mutate: refresh,
  } = useRequest<RetirementResponse, null, { id: string }, AdminRetirementQuery>({
    url: "/admin/retirements/{id}",
    pathParams: {
      id: retirementId as string,
    },
    queryParams: {
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
      ],
    },
  });

  const asset = useMemo(() => retirement?.assets?.at?.(0) as RctAssetResponse, [retirement?.assets]);

  useEffect(() => {
    if (asset) {
      const mapResult = mapTransactionProjectVintageData(asset?.associatedVintages);
      setProjectVintages(mapResult);
    }
  }, [asset]);

  return (
    <CheckStatus data={retirement} isLoading={isLoading} error={error}>
      <RetirementCompositionTable
        rows={projectVintages}
        bookId={asset?.rct.id}
        transactionId={retirement?.id}
        onRefresh={refresh}
        sourceId={asset?.rct?.id}
      />
    </CheckStatus>
  );
}
