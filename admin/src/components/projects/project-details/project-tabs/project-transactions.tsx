import {
  AdminProjectResponse,
  OrderByDirection,
  PermissionEnum,
  TransactionOrderByOptions,
  TransactionQueryResponse,
  TransactionResponse,
  TransactionType,
  TrimmedAssetFlowResponse,
} from "@rubiconcarbon/shared-types";
import TableBox from "@/components/ui/table-box/table-box";
import EnhancedTable, { ColDef, SortOrder } from "@/components/ui/table/enhanced-table";
import { useLogger } from "@/providers/logging";
import { MISSING_DATA, SERVER_PAGINATION_LIMIT } from "@/constants/constants";
import { useContext, useEffect, useMemo, useState } from "react";
import { AuthContext } from "@/providers/auth-provider";
import { useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import dateFormatterEST from "@/utils/formatters/estDateFormatter";
import { capitalize, isEmpty } from "lodash";
import currencyFormat from "@/utils/formatters/currencyFormat";
import integerFormat from "@/utils/formatters/integerFormat";
import StatusChip from "@/components/ui/status-chip/StatusChip";

export default function ProjectTransactions(props: { project: AdminProjectResponse }): JSX.Element {
  const { project } = props;
  const [projectTransactions, setProjectTransactions] = useState<TransactionResponse[]>([]);
  const { user } = useContext(AuthContext);
  const { logger } = useLogger();
  const hasSalesPermission = useMemo(() => user?.hasPermission(PermissionEnum.CUSTOMER_SALES_READ), [user]);
  const hasTradesPermission = useMemo(() => user?.hasPermission(PermissionEnum.TRADES_READ), [user]);
  const projectVintages = useMemo(() => project?.projectVintages?.map((pv) => pv?.id) ?? [], [project]);

  const columnsDef: ColDef[] = [
    {
      columnName: "updatedAt",
      displayName: "Updated Date",
      formatter: { func: dateFormatterEST },
      exportFormatter: { func: dateFormatterEST },
    },
    {
      columnName: "uiKey",
      displayName: "Transaction Key",
    },
    {
      columnName: "assetFlows",
      displayName: "Vintage",
      formatter: { func: (af: TrimmedAssetFlowResponse[]) => (!isEmpty(af) ? af[0]?.asset?.label : MISSING_DATA) },
      exportFormatter: {
        func: (af: TrimmedAssetFlowResponse[]) => (!isEmpty(af) ? af[0]?.asset?.label : MISSING_DATA),
      },
    },
    {
      columnName: "counterpartyName",
      displayName: "Counterparty",
    },
    {
      columnName: "type",
      displayName: "Type",
      formatter: { func: (t: string) => capitalize(t) },
      exportFormatter: { func: (t: string) => capitalize(t) },
    },
    {
      columnName: "totalQuantity",
      displayName: "Quantity",
      formatter: { func: integerFormat },
      exportFormatter: { func: integerFormat },
    },
    {
      columnName: "totalPrice",
      displayName: "Price",
      formatter: { func: currencyFormat },
      exportFormatter: { func: currencyFormat },
    },
    {
      columnName: "status",
      displayName: "Status",
      formatter: { func: (s: string) => <StatusChip status={s} /> },
      exportFormatter: { func: (s: string) => capitalize(s) },
    },
  ];

  const { data: transactionsResponse, trigger: getTransactions } = useTriggerRequest<TransactionQueryResponse>({
    url: "/admin/transactions",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      assetIds: project?.projectVintages?.map((pv) => pv?.id),
      types: [
        hasSalesPermission ? TransactionType.PURCHASE : null,
        hasTradesPermission ? TransactionType.TRADE : null,
      ]?.filter((entry) => !!entry),
      orderBys: [`${TransactionOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
    },
    swrOptions: {
      onSuccess: (data: TransactionQueryResponse) => setProjectTransactions(data?.data),
      onError: (error: any) => {
        setProjectTransactions([]);
        logger.error(`Unable to fetch sale and trade transactions. Error: ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    projectVintages?.length > 0 ? getTransactions() : setProjectTransactions([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectVintages]);

  const getFilteredData = (input: string): void => {
    const searchString = input.toUpperCase();
    const filteredData = transactionsResponse?.data.filter(
      (row) =>
        row?.updatedAt?.toString()?.toUpperCase().includes(searchString) ||
        row?.uiKey?.toUpperCase().includes(searchString) ||
        row?.counterpartyName?.toUpperCase().includes(searchString) ||
        row?.type?.toUpperCase().includes(searchString) ||
        row?.totalPrice?.toString()?.toUpperCase().includes(searchString) ||
        row?.totalQuantity?.toString()?.toUpperCase().includes(searchString) ||
        row?.status?.toUpperCase().includes(searchString) ||
        row?.assetFlows?.[0]?.asset?.label?.toUpperCase().includes(searchString),
    );
    setProjectTransactions(filteredData);
  };

  return (
    <TableBox>
      <EnhancedTable
        name={"project_transactions"}
        columnsDef={columnsDef}
        exportable={true}
        data={projectTransactions}
        rowsCountPerPage={100}
        getFilteredData={getFilteredData}
        defaultSort={{ columnName: "updatedAt", order: SortOrder.DESC }}
      />
    </TableBox>
  );
}
