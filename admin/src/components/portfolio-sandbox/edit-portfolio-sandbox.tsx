import React, { useContext } from "react";
import useS<PERSON> from "swr";
import { AxiosContext } from "@/providers/axios-provider";
import { ModelPortfolioResponse } from "@rubiconcarbon/shared-types";
import { Box } from "@mui/material";
import PortfolioSandboxComponents from "./portfolio-sandbox-table/portfolio-sandbox-components";
import Maybe from "../maybe/maybe";

export default function EditPortfolioSandbox(props: { id: string }): JSX.Element {
  const { id } = props;
  const { apiFetcher } = useContext(AxiosContext);

  const { data: mockBasket, mutate: refresh } = useSWR<ModelPortfolioResponse>(`/admin/model-portfolios/${id}`, {
    fetcher: apiFetcher as any,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  });

  return (
    <Box mt={2}>
      <Maybe condition={!!mockBasket}>
        <PortfolioSandboxComponents mockBasket={mockBasket} refreshPortfolio={refresh} />
      </Maybe>
    </Box>
  );
}
