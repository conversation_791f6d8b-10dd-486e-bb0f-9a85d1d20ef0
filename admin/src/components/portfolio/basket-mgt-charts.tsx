import { Box } from "@mui/material";
import React, { useState, useEffect, useMemo } from "react";
import StackedBar from "../ui/charts/stacked-bar";
import { ProgressBarChartData } from "../ui/charts/stacked-bar";
import { AdminBookResponse, BookType } from "@rubiconcarbon/shared-types";
import Maybe from "../maybe/maybe";
import COLORS from "../ui/theme/colors";

interface BasketMgtChartsProps {
  book: AdminBookResponse;
}
export default function BasketMgtCharts(props: BasketMgtChartsProps): JSX.Element {
  const { book } = props;
  const [chartsCollection, setChartsCollection] = useState<ProgressBarChartData[]>();

  const bookGeneratedRcts = book?.assetAllocationsByBookType.find((f) => f.bookType === BookType.PORTFOLIO_DEFAULT);
  const customerHeldRcts = book?.assetAllocationsByBookType.find((f) => f.bookType === BookType.PORTFOLIO_CUSTOMER);

  const totalBarValue = bookGeneratedRcts?.totalAmountAllocated ?? 0;

  // @kofi why does this need to be useMemo?
  const pendingPurchasePercent = useMemo(
    () => ((bookGeneratedRcts?.totalAmountPendingPurchase ?? 0) * 100) / totalBarValue,
    [bookGeneratedRcts?.totalAmountPendingPurchase, totalBarValue],
  );

  useEffect(() => {
    if (!!book) {
      setChartsCollection([
        {
          name: "available to sell",
          value: bookGeneratedRcts?.totalAmountAvailable ?? 0,
          title: "Available to sell",
          emptyValuesPlaceholder: false,
          style: {
            barColor: COLORS.chartOrange,
            title: {
              fontSize: "14px",
              padding: [0, 0, 70, 0],
              align: "left",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, 50, 0],
              align: "left",
            },
          },
        },
        {
          name: "pending purchase",
          value: bookGeneratedRcts?.totalAmountPendingPurchase ?? 0,
          title: "Pending purchase",
          emptyValuesPlaceholder: false,
          labelPosition: "right",
          style: {
            barColor: COLORS.chartPaleGreen,
            title: {
              fontSize: "14px",
              padding: [0, 0, -70, 0],
              align: "right",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, -14, 0],
              align: "right",
            },
          },
        },
        {
          name: "customer holding",
          value: customerHeldRcts?.totalAmountAllocated ?? 0,
          title: "Customer holding",
          emptyValuesPlaceholder: false,
          labelPosition: "right",
          style: {
            barColor: COLORS.chartsPurple,
            title: {
              fontSize: "14px",
              padding: [0, 0, 55, 0],
              align: "right",
            },
            value: {
              fontSize: "14px",
              padding: [0, 0, 40, 0],
              align: "right",
            },
          },
        },
      ]);
    }
  }, [book, bookGeneratedRcts, customerHeldRcts, pendingPurchasePercent]);

  return (
    <Box sx={{ minWidth: "200px" }}>
      <Maybe condition={!!chartsCollection}>
        <StackedBar chartsArray={chartsCollection} />
      </Maybe>
    </Box>
  );
}
