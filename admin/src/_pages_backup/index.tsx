import Maybe from "@/components/maybe/maybe";
import NestedMenuSection from "@/components/nested-menu-section/nested-menu-section";
import useNavigationMenu from "@/providers/navigation-menu-provider";

export default function Home(): JSX.Element {
  const { permissibleMenus } = useNavigationMenu();

  return (
    <Maybe condition={permissibleMenus.length > 1}>
      <NestedMenuSection menus={permissibleMenus.slice(1)} />
    </Maybe>
  );
}
