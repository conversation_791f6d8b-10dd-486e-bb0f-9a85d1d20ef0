import React from "react";
import {
  OrderByDirection,
  PermissionEnum,
  RetirementOrderByOptions,
  AdminRetirementQueryResponse,
  AdminRetirementQuery,
  RetirementRelations,
} from "@rubiconcarbon/shared-types";
import RetirementSummary from "./components/retirements-summary";
import { CheckStatus } from "../ui/check-staus/check-status";
import useNavigation from "@/utils/hooks/useNavigation";
import { Box } from "@mui/material";
import GenericTable from "../ui/generic-table";
import { useLogger } from "@/providers/logging";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import { AddRounded, FileDownloadRounded } from "@mui/icons-material";
import { DEFAULT_EXPORT_STYLE } from "../ui/generic-table/constants/generic-table-styles";
import useGenericTableUtility from "../ui/generic-table/hooks/use-generic-table-utility";
import { COLUMNS } from "./constants/columns";
import { toRetirementModel } from "@/utils/helpers/transaction/to-transaction-models";
import { TransactionModel } from "@/models/transaction";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";

export default function RetirementsTable(): JSX.Element {
  const { pushOnToPath } = useNavigation();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const {
    data: retirementsResponse,
    error,
    isLoading: loadingRetirements,
    isValidating: reloadingRetirements,
    mutate: refreshRetirements,
  } = useRequest<AdminRetirementQueryResponse, null, null, AdminRetirementQuery>({
    url: "/admin/retirements",
    queryParams: {
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
      includeRelations: [
        RetirementRelations.CUSTOMER_PORTFOLIO,
        RetirementRelations.ASSETS,
        RetirementRelations.RCT_VINTAGES,
        RetirementRelations.REQUESTED_BY,
      ],
      orderBys: [`${RetirementOrderByOptions.CREATED_AT}:${OrderByDirection.DESC_NULLS_LAST}`],
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch retirements.");
        logger.error(`Unable to fetch retirements. Error: ${error?.message}`, {});
      },
    },
  });

  const { table } = useGenericTableUtility<TransactionModel>({});

  return (
    <CheckStatus isLoading={loadingRetirements || reloadingRetirements} error={error}>
      <GenericTable
        id="Retirements"
        loading={loadingRetirements || reloadingRetirements}
        toRowModel={toRetirementModel}
        columns={COLUMNS}
        pageableData={retirementsResponse}
        globalSearch={{
          searchKeys: [
            "uiKey",
            "type",
            "product",
            "retirement.organization.name",
            "transfer.organization.name",
            "status",
            "amount",
            "dateStarted",
            "dateFinished",
            "memo",
            "retirement.beneficiary",
            "retirement.isPublic",
          ],
        }}
        export={{
          filename: `Retirements-${new Date()}`,
          setClientCanExport: table?.setClientCanExport,
          bindClientExport: table?.bindClientExport,
        }}
        styles={{
          root: {
            maxHeight: "calc(100vh - 145px)",
          },
        }}
        toolbarActionButtons={[
          {
            children: "Export",
            startIcon: <FileDownloadRounded />,
            requiredPermission: PermissionEnum.RETIREMENTS_READ,
            style: DEFAULT_EXPORT_STYLE,
            isDisabled: loadingRetirements || reloadingRetirements || !table?.clientCanExport,
            onClickHandler: table?.handleClientExport,
          },
          {
            children: "Retirement Calculator",
            requiredPermission: PermissionEnum.RETIREMENTS_CLEAR_ADMIN_REVIEW,
            isDisabled: loadingRetirements || reloadingRetirements,
            onClickHandler: () => pushOnToPath("retirement-calculator"),
          },
          {
            children: "Buffer Calculator",
            isDisabled: loadingRetirements || reloadingRetirements,
            onClickHandler: () => pushOnToPath("buffer-calculator"),
          },
          {
            children: "Retirement",
            startIcon: <AddRounded />,
            requiredPermission: PermissionEnum.RETIREMENTS_CREATE,
            isDisabled: loadingRetirements || reloadingRetirements,
            onClickHandler: () => pushOnToPath("/new-retirement"),
          },
          {
            children: "Transfer",
            startIcon: <AddRounded />,
            requiredPermission: PermissionEnum.RETIREMENTS_CREATE,
            isDisabled: loadingRetirements || reloadingRetirements,
            onClickHandler: () => pushOnToPath("/new-transfer"),
          },
        ]}
        isExpandable
        eager={{
          expand: true,
        }}
        renderExpandContent={(row) => (
          <Box padding={2}>
            <RetirementSummary transaction={row} refresh={refreshRetirements} />
          </Box>
        )}
      />
    </CheckStatus>
  );
}
