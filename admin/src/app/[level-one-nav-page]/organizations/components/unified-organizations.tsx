<<<<<<< HEAD:src/app/[level-one-nav-page]/organizations/components/unified-organizations.tsx
import { Typography } from "@mui/material";
import { GenericTabItem, GenericTabKey, GenericTabs, Match, Undefinable, useTriggerRequest } from "@rubiconcarbon/frontend-shared";
import { useStoreProvider } from "@providers/store-provider";
import {
  CounterpartyQuery,
  CounterpartyQueryResponse,
  OrganizationQuery,
  OrganizationResponse,
  PermissionEnum,
} from "@rubiconcarbon/shared-types";
import { useLogger } from "@providers/logging";
import useSnackbarVariants from "@hooks/use-enqueue-variant";
import useNavigation from "@/hooks/use-navigation";
import { useToggle } from "react-use";
import usePerformantState from "@/hooks/use-perfomant-state";
import { OrganizationType } from "@constants/organization-type.enum";
import { UnifiedOrganizationModel } from "@models/organization";
import { OrganizationTableExtensions } from "@/types/organization-table-extensions";
import useAuth from "@providers/auth-provider";
import { useMemo } from "react";
import { SERVER_PAGINATION_LIMIT } from "@constants/constants";
import GenericTable from "@components/ui/generic-table";
import { PORTAL_COLUMNS } from "../constants/portal-columns";
import { DEFAULT_PAGE_LIMIT } from "@components/ui/generic-table/state";
import { toUnifiedOrganizationModel } from "../utilities/to-model";
import MatIcon from "@components/ui/mat-icon/mat-icon";
import { COUNTERPARTY_COLUMNS } from "../constants/counterparty-columns";
import CounterpartySummary from "./counterparty-summary";
import OrganizationStatusModal from "./status-modal";
import OnboardingModal from "./onboarding-modal";
import COLORS from "@components/ui/theme/colors";

import classes from "../styles/unified-organizations.module.scss";

type TabsTypes = "portal" | "counterparty";
=======
import { useRequest } from "@rubiconcarbon/frontend-shared";
import { AdminOrganizationQuery, AdminOrganizationQueryResponse, PermissionEnum } from "@rubiconcarbon/shared-types";
import { useLogger } from "@/providers/logging";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import GenericTable from "../ui/generic-table";
import { toOrganizationModel } from "./utilities/to-model";
import MatIcon from "../ui/mat-icon/mat-icon";
import useNavigation from "@/utils/hooks/useNavigation";
import { AuthContext } from "@/providers/auth-provider";
import { useContext } from "react";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";
import { COLUMNS } from "./constants/columns";
>>>>>>> main:src/components/organizations/index.tsx

const UnifiedOrganizations = ({
  organizationResponse: serverOrganizationResponse,
  counterpartiesResponse: serverCounterpartiesResponse,
}: {
  organizationResponse: OrganizationResponse[];
  counterpartiesResponse: CounterpartyQueryResponse;
}): JSX.Element => {
  const { logger } = useLogger();
  const { user } = useAuth();
  const { enqueueError } = useSnackbarVariants();
<<<<<<< HEAD:src/app/[level-one-nav-page]/organizations/components/unified-organizations.tsx
  const { ephemeralState, updateEphemeralState } = useStoreProvider();
  const { pushToPath } = useNavigation();

  const [statusModalOpen, toggleStatusModal] = useToggle(false);
  const [onboardModalOpen, toggleOnboardModal] = useToggle(false);

  const [viewingRow, setViewingRow] = usePerformantState<Undefinable<UnifiedOrganizationModel>>(undefined);

  const hasCustomerPortalOrgPermission = useMemo(() => user?.hasPermission(PermissionEnum.ORGANIZATIONS_READ), [user]);
  const hasTradeCounterPartyPermission = useMemo(() => user?.hasPermission(PermissionEnum.COUNTERPARTIES_READ), [user]);

  const tabs = useMemo(
    () =>
      [
        hasCustomerPortalOrgPermission
          ? {
              key: "portal",
              data: "Customer Portal Organizations",
            }
          : null,
        hasTradeCounterPartyPermission
          ? {
              key: "counterparty",
              data: "Trade Counterparty",
            }
          : null,
      ]?.filter((entry) => !!entry),
    [hasCustomerPortalOrgPermission, hasTradeCounterPartyPermission],
  ) as GenericTabItem<TabsTypes, string>[];

  const { organizations } = ephemeralState;
  const { viewing: tab } = organizations;

  const {
    data: portalOrganizations = [],
    isMutating: loadingPortalOrganizations,
    trigger: getPortalOrganizations,
  } = useTriggerRequest<OrganizationResponse[], object, object, OrganizationQuery>({
=======
  const { pushOnToPath } = useNavigation();

  const { data: organizationsResponse, isLoading: loadingOrganizations } = useRequest<
    AdminOrganizationQueryResponse,
    object,
    object,
    AdminOrganizationQuery
  >({
>>>>>>> main:src/components/organizations/index.tsx
    url: "/admin/organizations",
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
    },
    optimisticData: serverOrganizationResponse,
    swrOptions: {
      onError: (error) => {
        enqueueError("Unable to fetch organizations.");
        logger.error(`Unable to fetch organizations. Error: ${error?.message}`, {});
      },
    },
  });

<<<<<<< HEAD:src/app/[level-one-nav-page]/organizations/components/unified-organizations.tsx
  const {
    data: counterpartiesResponse,
    isMutating: loadingCounterparties,
    trigger: getCounterparties,
  } = useTriggerRequest<CounterpartyQueryResponse, object, object, CounterpartyQuery>({
    url: "/admin/counterparties",
    queryParams: {
      offset: 0,
      limit: SERVER_PAGINATION_LIMIT,
      includeTotalCount: true,
    },
    optimisticData: serverCounterpartiesResponse,
    swrOptions: {
      onError: (error) => {
        enqueueError("Unable to fetch counterparties.");
        logger.error(`Unable to fetch counterparties. Error: ${error?.message}`, {});
      },
    },
  });

  const renderTab = (tab: string): JSX.Element => (
    <Typography className={classes.TabText} color={COLORS.rubiconGreen} variant="body2">
      {tab}
    </Typography>
  );

  return (
    <>
      <GenericTabs
        tabs={tabs}
        value={tab}
        renderTab={renderTab}
        onTabChange={(key: GenericTabKey): void => updateEphemeralState("organizations.viewing", key)}
        classes={{
          root: classes.Tabs,
          tab: classes.Tab,
          active: classes.Active,
        }}
      />
      <Match
        value={tab}
        cases={[
          {
            case: "portal",
            component: (
              <GenericTable
                id="portal"
                loading={loadingPortalOrganizations}
                eager={{
                  expand: true,
                }}
                pageableData={{
                  data: portalOrganizations,
                  page: {
                    offset: 0,
                    limit: DEFAULT_PAGE_LIMIT,
                    size: portalOrganizations?.length || 0,
                  },
                }}
                columns={PORTAL_COLUMNS}
                toRowModel={toUnifiedOrganizationModel}
                globalSearch={{
                  searchKeys: [
                    "name",
                    "rubiconManager.name",
                    "salesforceIdentifier",
                    "createdAtF",
                    "onboardingStatusF",
                  ],
                }}
                sort={{
                  sorts: {
                    createdAtF: "desc",
                  },
                }}
                toolbarActionButtons={[
                  {
                    children: "Customer Portal",
                    startIcon: (
                      <MatIcon
                        value="add"
                        variant="round"
                        size={20}
                        sx={{ color: user?.hasPermission(PermissionEnum.ORGANIZATIONS_CREATE) ? "white" : "gray" }}
                      />
                    ),
                    isDisabled: loadingPortalOrganizations,
                    requiredPermission: PermissionEnum.ORGANIZATIONS_CREATE,
                    onClickHandler: () => pushToPath("/create-portal-organization"),
                  },
                ]}
                styles={{
                  root: {
                    maxHeight: "calc(100vh - 250px)",
                  },
                }}
                extensions={
                  {
                    toggleStatusModal,
                    toggleOnboardModal,
                    setViewingRow,
                  } as OrganizationTableExtensions
                }
              />
            ),
          },
          {
            case: "counterparty",
            component: (
              <GenericTable
                id="counterparty"
                loading={loadingCounterparties}
                isExpandable
                eager={{
                  expand: true,
                }}
                pageableData={counterpartiesResponse}
                columns={COUNTERPARTY_COLUMNS}
                toRowModel={toUnifiedOrganizationModel}
                globalSearch={{
                  searchKeys: ["name", "createdAtF", "onboardingStatusF"],
                }}
                sort={{
                  sorts: {
                    createdAtF: "desc",
                  },
                }}
                toolbarActionButtons={[
                  {
                    children: "Trade Counterparty",
                    startIcon: (
                      <MatIcon
                        value="add"
                        variant="round"
                        size={20}
                        sx={{ color: user?.hasPermission(PermissionEnum.COUNTERPARTIES_CREATE) ? "white" : "gray" }}
                      />
                    ),
                    isDisabled: loadingCounterparties,
                    requiredPermission: PermissionEnum.COUNTERPARTIES_CREATE,
                    onClickHandler: () => pushToPath("/create-trade-counterparty"),
                  },
                ]}
                styles={{
                  root: {
                    maxHeight: "calc(100vh - 250px)",
                  },
                }}
                extensions={
                  {
                    toggleStatusModal,
                    toggleOnboardModal,
                    setViewingRow,
                  } as OrganizationTableExtensions
                }
                renderExpandContent={(row) => <CounterpartySummary counterparty={row} />}
              />
            ),
          },
        ]}
      />
      <OrganizationStatusModal
        type={tab === "portal" ? OrganizationType.Portal : OrganizationType.Counterparty}
        model={viewingRow}
        isOpen={statusModalOpen}
        onSave={async () => {
          toggleStatusModal();
          if (tab === "portal") {
            await getPortalOrganizations();
          } else {
            await getCounterparties();
          }
        }}
        onClose={() => {
          toggleStatusModal();
          setViewingRow(undefined);
        }}
      />
      <OnboardingModal
        type={tab === "portal" ? OrganizationType.Portal : OrganizationType.Counterparty}
        model={viewingRow}
        isOpen={onboardModalOpen}
        onSave={async () => {
          toggleOnboardModal();
          if (tab === "portal") {
            await getPortalOrganizations();
          } else {
            await getCounterparties();
          }
        }}
        onClose={() => {
          toggleOnboardModal();
          setViewingRow(undefined);
        }}
      />
    </>
=======
  return (
    <GenericTable
      id="portal"
      loading={loadingOrganizations}
      eager={{
        expand: true,
      }}
      pageableData={organizationsResponse}
      columns={COLUMNS}
      toRowModel={toOrganizationModel}
      globalSearch={{
        searchKeys: ["name", "tag", "customerAccessStatusF", "isOnboardedStatusF", "createdAtF"],
      }}
      sort={{
        sorts: {
          createdAtF: "desc",
        },
      }}
      toolbarActionButtons={[
        {
          children: "Organization",
          startIcon: (
            <MatIcon
              value="add"
              variant="round"
              size={20}
              sx={{ color: user.hasPermission(PermissionEnum.ORGANIZATIONS_CREATE) ? "white" : "gray" }}
            />
          ),
          isDisabled: loadingOrganizations,
          requiredPermission: PermissionEnum.ORGANIZATIONS_CREATE,
          onClickHandler: () => pushOnToPath("/create-organization"),
        },
      ]}
      styles={{
        root: {
          maxHeight: "calc(100vh - 250px)",
        },
      }}
    />
>>>>>>> main:src/components/organizations/index.tsx
  );
};

export default UnifiedOrganizations;
