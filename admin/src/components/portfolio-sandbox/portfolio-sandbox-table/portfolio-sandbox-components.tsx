import React, { useState, useEffect, useContext, useMemo } from "react";
import useS<PERSON> from "swr";
import Decimal from "decimal.js";
import { Box } from "@mui/material";
import {
  uuid,
  ModelPortfolioResponse,
  ModelPortfolioComponentResponse,
  AdminProjectQueryResponse,
  AdminProjectVintageQueryResponse,
  BufferCategoryResponse,
  PermissionEnum,
  ProjectRelations,
  ProjectVintageRelations,
} from "@rubiconcarbon/shared-types";
import { AxiosContext } from "@/providers/axios-provider";
import { isEmpty } from "lodash";
import Maybe from "@/components/maybe/maybe";
import { KeyedMutator } from "swr";
import {
  Project,
  PortfolioComponentResponse,
  getAmountAvailable,
  ProjectPopulationType,
  fillMissingProjectInfo,
  getHoldingAmount,
  getShortageAmount,
} from "./portfolio-sandbox-model";
import { pickFromArrayOfRecords } from "@/utils/helpers/general/data-structure";
import useProjectPricingRequest from "@/utils/hooks/useProjectPricingRequest";
import { PriceSourceEnum } from "@/constants/state";
import PortfolioSummary from "./portfolio-sandbox-summary";
import PortfolioSandboxHeader from "../portfolio-sandbox-header/portfolio-sandbox-header";
import PortfolioSandboxMemo from "../portfolio-sandbox-memo/portfolio-sandbox-memo";
import PortfolioSandboxTable from "./portfolio-sandbox-table";
import { generateQueryParams } from "@rubiconcarbon/frontend-shared";
import { SERVER_PAGINATION_LIMIT } from "@/constants/constants";
import { AuthContext } from "@/providers/auth-provider";
import LinkedPortfolios from "../linked-portfolios/linked-portfolios";

export default function PortfolioSandboxComponents(props: {
  mockBasket: ModelPortfolioResponse;
  refreshPortfolio: KeyedMutator<ModelPortfolioResponse>;
}): JSX.Element {
  const { mockBasket, refreshPortfolio } = props;
  const [basketComponents, setBasketComponents] = useState<ModelPortfolioComponentResponse[]>();
  const { api } = useContext(AxiosContext);
  const [availableProjects, setAvailableProjects] = useState<Project[]>();
  const [availableComponents, setAvailableComponents] = useState<PortfolioComponentResponse[]>();
  const [basketVintageIds, setBasketVintageIds] = useState<string[]>();
  const [fetchKey, setFetchKey] = useState<number>(0);
  const { apiFetcher } = useContext(AxiosContext);
  const { user: loginUser } = useContext(AuthContext);

  const [allProjectsComponents, setAllProjectsComponents] = useState<PortfolioComponentResponse[]>();
  const [availableProjectsComponents, setAvailableProjectsComponents] = useState<PortfolioComponentResponse[]>();

  const isRestricted: boolean = useMemo(
    () => !loginUser.hasPermission(PermissionEnum.MODEL_PORTFOLIOS_ADVANCED_VIEW),
    [loginUser],
  );

  //this flag exists in order to indicate if Sales Order should be allowed
  const isAmountAvailableShortage: boolean = useMemo(
    () =>
      !!availableComponents ? availableComponents.some((c) => c?.amountAvailableShotage < 0 || !c?.vintage?.id) : false,
    [availableComponents],
  );

  useEffect(() => {
    if (!!mockBasket && !isEmpty(mockBasket?.modelPortfolioComponents)) {
      setBasketComponents(mockBasket.modelPortfolioComponents);
    } else {
      setBasketComponents([]);
    }
  }, [mockBasket]);

  const { data: bufferCategories } = useSWR<BufferCategoryResponse[]>(`/admin/buffer-categories`, {
    fetcher: apiFetcher as any,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  });

  const { data: traderPricings } = useProjectPricingRequest({
    key: `${fetchKey}-latest-prices`,
    enable: !!availableComponents && basketVintageIds?.length > 0,
    limit: basketVintageIds?.length,
    source: PriceSourceEnum.RUBICON,
    requestBody: {
      vintage_ids: basketVintageIds,
    },
  });

  const projectIds: uuid[] = useMemo(
    () => (availableComponents?.length > 0 ? Array.from(new Set(availableComponents.map((c) => c.projectId))) : []),
    [availableComponents],
  );

  const tradersToLatestPrices: Record<string, Decimal> = useMemo(
    () =>
      traderPricings?.length
        ? traderPricings.reduce(
            (accum, record) => ({
              ...accum,
              [record.vintage_id]: record.price,
            }),
            {},
          )
        : {},
    [traderPricings],
  );

  useEffect(() => {
    if (!isEmpty(basketComponents)) {
      const newVintageIds: string[] =
        basketComponents.length > 0
          ? pickFromArrayOfRecords<ModelPortfolioComponentResponse, "vintageId">(basketComponents, "vintageId")
              .map(({ vintageId }) => vintageId)
              .filter((c) => !!c)
              .reduce((acc, curr) => (acc.includes(curr) ? acc : [...acc, curr]), [])
          : [];
      if (!!newVintageIds && newVintageIds.length > 0) {
        api
          .get<AdminProjectVintageQueryResponse>(
            `/admin/project-vintages?${generateQueryParams({
              ids: newVintageIds.join(","),
              limit: SERVER_PAGINATION_LIMIT,
              includeRelations: [
                ProjectVintageRelations.ASSET_ALLOCATIONS_BY_BOOK_TYPE,
                ProjectVintageRelations.PRICES,
              ],
            })}`,
          )
          .then((response) => {
            const vintagesComponents = response.data.data;
            const portfolioComponentResponse: PortfolioComponentResponse[] = basketComponents;
            if (!!vintagesComponents && vintagesComponents.length > 0) {
              const updatedComponents = portfolioComponentResponse.map((c) => {
                if (!!c.vintageId) {
                  const vintageResponse = vintagesComponents.find((v) => v.id === c.vintageId);
                  if (!!vintageResponse) {
                    const amountAvailable = getAmountAvailable(vintageResponse);
                    const holdingAmount = getHoldingAmount(vintageResponse);
                    c.costBasis = vintageResponse?.averageCostBasis;
                    c.bufferPercentage = vintageResponse?.riskBufferPercentage;
                    c.assetAllocationsByBookType = vintageResponse?.assetAllocationsByBookType;
                    //inventory shortage is calculated based on holding amount
                    c.inventoryShortage = +getShortageAmount(c.amountAllocated, holdingAmount);
                    c.amountAvailableShotage = +getShortageAmount(c.amountAllocated, amountAvailable);
                    c.amountAvailable = amountAvailable;
                    c.holdingAmount = holdingAmount;
                    c.vintageInterval = vintageResponse.name;
                  }
                }
                return c;
              });
              setAvailableComponents(fillMissingProjectInfo(availableProjects, updatedComponents));
              setBasketVintageIds(newVintageIds);
              setFetchKey((fetchKey) => fetchKey + 1);
            }
          });
      }
      setAvailableComponents(fillMissingProjectInfo(availableProjects, basketComponents));
    } else {
      setAvailableComponents([]);
    }
  }, [basketComponents, availableProjects, api]);

  useEffect(() => {
    if (!!tradersToLatestPrices && !isEmpty(availableComponents)) {
      const updatedComponents = availableComponents.map((component) => {
        if (!isEmpty(component.vintageId) && !!tradersToLatestPrices?.[component.vintageId]) {
          component.portfolioManagerEstimate = tradersToLatestPrices?.[component.vintageId];
        }
        return component;
      });
      setAvailableComponents(updatedComponents);
    }
  }, [tradersToLatestPrices]); // eslint-disable-line

  const { data: projects } = useSWR<AdminProjectQueryResponse>(
    !isEmpty(projectIds)
      ? `/admin/projects?${generateQueryParams({
          limit: SERVER_PAGINATION_LIMIT,
          includeRelations: [
            ProjectRelations.PROJECT_TYPE,
            ProjectRelations.COUNTRY,
            ProjectRelations.ASSET_ALLOCATIONS,
          ],
          ids: projectIds,
        })}`
      : null,
    {
      fetcher: apiFetcher as any,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    },
  );

  useEffect(() => {
    if (projects?.data?.length > 0) {
      const result: Project[] = projects.data
        ?.map((p) => ({
          registryProjectId: p.registryProjectId,
          id: p.id,
          name: p.name,
          rctStandard: p.rctStandard,
          suspended: p.suspended,
          type: p.projectType,
          isScienceTeamApproved: p.isScienceTeamApproved,
          country: p?.country,
          integrityGradeScore: p?.integrityGradeScore,
        }))
        .sort((a, b) => a?.registryProjectId?.localeCompare(b?.registryProjectId));
      setAvailableProjects(result);
    }
  }, [projects]);

  useEffect(() => {
    const available: PortfolioComponentResponse[] = [];
    const all: PortfolioComponentResponse[] = [];

    if (!isEmpty(availableComponents)) {
      availableComponents?.forEach((element) => {
        if (element?.project?.hasBalance) {
          available.push(element);
        } else {
          all.push(element);
        }
      });
    }
    setAvailableProjectsComponents(available);
    setAllProjectsComponents(all);
  }, [projects, availableComponents, isRestricted]);

  return (
    <>
      <Box>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <PortfolioSandboxHeader
            mockBasket={mockBasket}
            refreshPortfolio={refreshPortfolio}
            isRestricted={isRestricted}
            isAmountAvailableShortage={isAmountAvailableShortage}
          />
        </Box>
        <Maybe condition={!!bufferCategories}>
          <Box>
            <PortfolioSandboxTable
              mockBasket={mockBasket}
              hasEstimate={!!mockBasket?.priceEstimate}
              isDisabled={mockBasket?.showCustomer ?? true}
              availableComponents={availableProjectsComponents}
              availableProjects={availableProjects}
              bufferCategories={bufferCategories}
              refreshPortfolio={refreshPortfolio}
              isRestricted={isRestricted}
              projectsPopulationType={ProjectPopulationType.AVAILABLE}
            />
          </Box>
          <Box mt={4}>
            <PortfolioSandboxTable
              mockBasket={mockBasket}
              hasEstimate={!!mockBasket?.priceEstimate}
              isDisabled={mockBasket?.showCustomer ?? true}
              availableComponents={allProjectsComponents}
              availableProjects={availableProjects}
              bufferCategories={bufferCategories}
              refreshPortfolio={refreshPortfolio}
              isRestricted={isRestricted}
              projectsPopulationType={ProjectPopulationType.OTHER}
            />
          </Box>

          <Box mt={4}>
            <LinkedPortfolios portfolioId={mockBasket?.id} />
          </Box>
        </Maybe>
      </Box>
      <Maybe condition={!!bufferCategories && !isRestricted}>
        <Box mt={4}>
          <PortfolioSummary availableComponents={availableComponents} />
        </Box>
      </Maybe>
      <Box mt={4} mb={2}>
        <PortfolioSandboxMemo mockBasket={mockBasket} refreshPortfolio={refreshPortfolio} />
      </Box>
    </>
  );
}
