import React, { useEffect } from "react";
import { AdminProjectQueryResponse, ProjectRelations } from "@rubiconcarbon/shared-types";
import { CheckStatus } from "../ui/check-staus/check-status";
import useBreadcrumbs from "@/providers/breadcrumb-provider";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { useRequest } from "@rubiconcarbon/frontend-shared";
import ProjectDetails from "./project-details/project-details";
import { useLogger } from "@/providers/logging";

export default function ProjectItem(props: { projectId: string | string[] }): JSX.Element {
  const { projectId } = props;
  const { updateBreadcrumbName } = useBreadcrumbs();
  const { logger } = useLogger();
  const { enqueueError } = useSnackbarVariants();

  const {
    data: project,
    error,
    isLoading,
  } = useRequest<AdminProjectQueryResponse>({
    url: `/admin/projects`,
    queryParams: {
      includeRelations: [ProjectRelations.COUNTRY, ProjectRelations.BUFFER_CATEGORY, ProjectRelations.PROJECT_VINTAGES],
      ids: projectId,
    },
    swrOptions: {
      onError: (error: any) => {
        enqueueError("Unable to fetch project data.");
        logger.error(`Unable to fetch project data for project id: ${projectId}. Error: ${error?.message}`, {});
      },
    },
  });

  useEffect(() => {
    if (project?.data[0]) {
      updateBreadcrumbName("Project Details", project?.data[0]?.name);
    }
  }, [project, updateBreadcrumbName]);

  if (!projectId) return <p>No Data</p>;

  return (
    <CheckStatus data={project} isLoading={isLoading} error={error}>
      <ProjectDetails project={project?.data[0]} />
    </CheckStatus>
  );
}
