import { registerDecorator, ValidationOptions } from "class-validator";
import { FeeStructure } from "../models/marketing-agreement";
import { isNothing } from "@rubiconcarbon/frontend-shared";

export function ValidateFeeStructure(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string): void {
    registerDecorator({
      name: "validateFeeStructure",
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: {
        validate(value: FeeStructure[]) {
          if (!Array.isArray(value) || value.length === 0) return false;

          // Store specific error details
          const errorDetails: string[] = [];

          if (
            value?.some(
              (entry) => isNothing(entry?.minBracket, ["string"]) || isNothing(entry?.marketingFee, ["string"]),
            )
          )
            errorDetails.push("Required");
          else
            for (let i = 0; i < value.length; i++) {
              const currentEntry = value[i];

              // Cross-entry validation
              if (i > 0) {
                const previousEntry = value[i - 1];

                // Skip validation for entries with incomplete data
                if (
                  isNothing(currentEntry.minBracket, ["string"]) ||
                  (isNothing(currentEntry.maxBracket, ["string"]) && !currentEntry?.isAbove) ||
                  isNothing(previousEntry.maxBracket, ["string"])
                )
                  continue;

                const prevMaxBracket = parseFloat(previousEntry.maxBracket);
                const currentMinBracket = parseFloat(currentEntry.minBracket);

                // Current min must be greater or equal to previous max
                if (currentMinBracket < prevMaxBracket) {
                  errorDetails.push(
                    `Row ${i + 1}: Min bracket (${currentMinBracket}) must be greater than or equal to previous max bracket (${prevMaxBracket})`,
                  );
                }
              }
            }

          // Attach error details to the validation context
          (this as any).errorDetails = errorDetails;

          return errorDetails.length === 0;
        },
        defaultMessage(): string {
          const errorDetails = (this as any).errorDetails || [];
          return errorDetails.join("; ");
        },
      },
    });
  };
}
