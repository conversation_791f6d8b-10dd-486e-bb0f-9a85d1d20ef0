import { useRouter } from "next/router";
import Page from "@/components/layout/containers/page";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import UserPermissions from "@/components/users/user-permissions";
import { Authorize } from "@/services/authorize";

export default function UserPermissionsPage(): JSX.Element {
  const { query, isReady } = useRouter();

  if (isReady) {
    return (
      <Authorize permissions={[PermissionEnum.USERS_READ, PermissionEnum.ORGANIZATIONS_MANAGE_USERS]}>
        <Page>
          <UserPermissions userId={query["user-id"]} />
        </Page>
      </Authorize>
    );
  }
}
