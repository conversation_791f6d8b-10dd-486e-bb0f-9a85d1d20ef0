import {
  uuid,
  ModelPortfolioComponentResponse,
  AdminProjectVintageResponse,
  ProjectTypeResponse,
  BufferCategoryResponse,
  CountryResponse,
  BookTypeGroupedAllocationResponse,
  BookType,
} from "@rubiconcarbon/shared-types";
import Decimal from "decimal.js";
import integerFormat from "@/utils/formatters/integerFormat";
import { MISSING_DATA } from "@/constants/constants";
import { isEmpty } from "lodash";
import { AllRubiconAvailableBookTypes, AllRubiconHoldingBookTypes } from "./constants";
import { Box } from "@mui/material";
import { BookTypeMapping } from "@/mappers/book-type-mapper";

type ColValue = string | number | Project | Vintage | BufferCategory | Date | Decimal | boolean;

interface EnhancedTableRow {
  id: uuid | number | string;
}

export enum ProjectPopulationType {
  AVAILABLE,
  OTHER,
  NOT_APPLICABLE,
}

export interface PortfolioComponentResponse extends ModelPortfolioComponentResponse {
  inventoryShortage?: number;
  amountAvailableShotage?: number;
  amountAvailable?: number;
  holdingAmount?: number;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
}

export interface ColDef {
  id: string;
  title: string;
  mode?: "display" | "edit";
  type: "autocomplete" | "string" | "number" | "display";
  value: ColValue;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>, newValue?: any) => void;
  decimalScale?: number;
  prefix?: string;
  suffix?: string;
  helperText?: string;
  error?: boolean;
  formatter?: IDisplayFormatter;
  defaultProps?: DefaultProps<Project> | DefaultProps<Vintage>;
  class?: string;
  tooltip?: string | JSX.Element;
  hidden?: boolean;
  onSearchChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: () => void;
  onClear?: () => void;
  placeHolder?: string;
  valueFormatter?: (x: any) => any;
}

export interface Project {
  registryProjectId: string;
  id?: uuid;
  name?: string;
  type?: ProjectTypeResponse;
  inputValue?: string;
  rctStandard?: boolean;
  suspended?: boolean;
  isScienceTeamApproved?: boolean;
  bufferCategory?: BufferCategoryResponse;
  country?: CountryResponse;
  integrityGradeScore?: number;
}

export interface BufferCategory {
  id?: uuid;
  name?: string;
}

export interface Vintage {
  id?: uuid;
  name: string;
  inputValue?: string;
  amountAvailable?: number;
  holdingAmount?: number;
  latestTraderPrice?: Decimal;
  averageCostBasis?: Decimal;
  riskBufferPercentage?: Decimal;
  bufferCategory?: BufferCategory;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
}

interface IFormField {
  error?: boolean;
  message?: string;
  tooltip?: string | JSX.Element;
}

export interface StringFormField extends IFormField {
  value?: string;
}

export interface ProjectFormField extends IFormField {
  value?: Project;
}

export interface VintageFormField extends IFormField {
  value?: Vintage;
}

export interface BufferCategoryField extends IFormField {
  value?: BufferCategory;
}

interface IDisplayFormatter {
  func: (input: ColValue | Map<string, ColValue>) => any;
  inputFields?: string[];
  overrideMissingDataDisplay?: boolean;
}

type DefaultProps<T> = {
  options: T[];
  getOptionLabel: (option: T) => string;
  isOptionEqualToValue: (option: T, value: T) => boolean;
};

export interface PortfolioRow {
  id?: uuid;
  registryProjectId?: ProjectFormField;
  projectName?: StringFormField;
  projectType?: StringFormField;
  vintageInterval?: VintageFormField;
  bufferPercentage?: StringFormField;
  bufferCategory?: BufferCategoryField;
  amountAllocated: StringFormField;
  costBasis?: StringFormField;
  inventoryShortage?: StringFormField;
  portfolioManagerEstimate?: StringFormField;
  integrityGradeScore?: StringFormField;
  assetAllocationsByBookType?: BookTypeGroupedAllocationResponse[];
  amountAvailable?: number;
}

export const getShortageAmount = (quantity: number, totalAmount: number): string => {
  const result = "0";
  if (!!totalAmount && isFinite(quantity)) {
    const amount = totalAmount - quantity;
    if (amount < 0) {
      return amount.toString();
    } else {
      return "0";
    }
  } else if (!totalAmount && isFinite(quantity) && quantity > 0) {
    return (-quantity).toString();
  }
  return result;
};

export const generateShortageBreakdown = (
  amount: number,
  assetAllocationsByBookType: BookTypeGroupedAllocationResponse[],
  bookTypes: BookType[],
  title: string,
): JSX.Element => {
  const result: string[] = [];
  const relevantBooks = assetAllocationsByBookType?.filter((b) => bookTypes.includes(b?.bookType));

  relevantBooks?.forEach((b) =>
    result.push(`${BookTypeMapping[b.bookType]}: ${integerFormat(b.totalAmountAllocated)}`),
  );
  return (
    <Box>
      <span>
        <strong>
          <u>{title}</u>
        </strong>
      </span>
      <br />
      {result.map((c, idx) => (
        <span key={idx}>
          {c}
          <br></br>
        </span>
      ))}
      <br />
      <span>
        <strong>Total: {integerFormat(amount)}</strong>
      </span>
    </Box>
  );
};

export const getInventoryShortage = (
  quantity: number,
  holdingAmount: number,
  assetAllocationsByBookType: BookTypeGroupedAllocationResponse[],
): StringFormField => {
  const inventoryShortage: StringFormField = {
    value: "",
    tooltip: "",
  };

  if (!!holdingAmount && isFinite(quantity)) {
    inventoryShortage.value = getShortageAmount(quantity, holdingAmount);

    inventoryShortage.tooltip = generateShortageBreakdown(
      holdingAmount,
      assetAllocationsByBookType,
      AllRubiconHoldingBookTypes,
      "Asset Allocation",
    );
  } else if (!holdingAmount && isFinite(quantity) && quantity > 0) {
    inventoryShortage.value = (-quantity).toString();
    //generateShortageBreakdown(holdingAmount, assetAllocationsByBookType, AllRubiconHoldingBookTypes);
  }
  return inventoryShortage;
};

export const getInventoryShortageClass = (inventory: string): string => {
  if (!!inventory && +inventory < 0) {
    return "negativeInventory";
  }

  return "";
};

function getFormatterInputFields<T extends EnhancedTableRow>(
  value: ColValue,
  inputFields: string[],
  row: T,
): ColValue | Map<string, ColValue> {
  if (!inputFields) return value;

  const map = new Map<string, ColValue>();
  inputFields.forEach((element) => {
    map.set(element, row[element]);
  });

  return map;
}

export function formatCellValue<T extends EnhancedTableRow>(value: ColValue, colDef: ColDef, row?: T): string {
  const formatterInputFields = getFormatterInputFields(value, colDef?.formatter?.inputFields, row);
  if (!colDef?.formatter) return !value ? MISSING_DATA : value.toString();
  return colDef?.formatter.func(formatterInputFields);
}

export const validateRequiredStringInput = (input: StringFormField): StringFormField => {
  let newValue: StringFormField = {};
  if (isEmpty(input.value)) {
    newValue = {
      value: input.value,
      error: true,
      message: "value is required",
    };
  } else {
    newValue = {
      value: input.value,
      error: false,
      message: "",
    };
  }

  return newValue;
};

export const validateRequiredNumberInput = (input: StringFormField): StringFormField => {
  if (isEmpty(input.value)) {
    return {
      value: input.value,
      error: true,
      message: "value is required",
    };
  }

  if (convertStringToNumber(input.value) <= 0) {
    return {
      value: input.value,
      error: true,
      message: "value should be greater than 0",
    };
  }

  return {
    value: input.value,
    error: false,
    message: "",
  };
};

export const getEmptyPorrtfolioRow = (): PortfolioRow => {
  return {
    registryProjectId: { value: { registryProjectId: null } },
    projectName: { value: "" },
    projectType: { value: "" },
    vintageInterval: { value: null },
    bufferPercentage: { value: "" },
    amountAllocated: { value: "0" },
    inventoryShortage: { value: "0" },
    costBasis: { value: "" },
    portfolioManagerEstimate: { value: "" },
    bufferCategory: { value: null },
    integrityGradeScore: { value: "" },
  };
};

export const isValidRow = (portfolioRow: PortfolioRow): boolean => {
  for (const value of Object.values(portfolioRow)) {
    // eslint-disable-line
    if (value?.error) return false;
  }

  return true;
};

export const validateRow = (row: PortfolioRow): PortfolioRow => {
  const validatedRow = { ...row };
  validatedRow.amountAllocated = validateRequiredNumberInput(validatedRow.amountAllocated);

  if (!validatedRow.registryProjectId?.value?.registryProjectId) {
    validatedRow.registryProjectId = {
      ...validatedRow.registryProjectId,
      error: true,
      message: "value is required",
    };
  } else {
    validatedRow.registryProjectId = {
      ...validatedRow.registryProjectId,
      error: false,
      message: "",
    };
  }

  if (!validatedRow.vintageInterval?.value?.name) {
    validatedRow.vintageInterval = {
      ...validatedRow.vintageInterval,
      error: true,
      message: "value is required",
    };
  } else {
    validatedRow.vintageInterval = {
      ...validatedRow.vintageInterval,
      error: false,
      message: "",
    };
  }

  return validatedRow;
};

export function convertStringToNumber(input: string): number {
  if (!!input) return +input.replaceAll(",", "").replaceAll("$", "").replaceAll("%", "");
}

export function handleQuantityChange(
  portfolioRow: PortfolioRow,
  newQuantity: string,
  projectsPopulationType: ProjectPopulationType,
): PortfolioRow {
  const row = { ...portfolioRow };
  row.amountAllocated.value = newQuantity;

  const amountAvailable = portfolioRow?.vintageInterval?.value?.amountAvailable;
  const holdingAmount = portfolioRow?.vintageInterval?.value?.holdingAmount;
  const quantity = convertStringToNumber(newQuantity);
  if (quantity > 0) {
    row.amountAllocated.error = false;
    row.amountAllocated.message =
      projectsPopulationType === ProjectPopulationType.AVAILABLE
        ? `${isFinite(holdingAmount) ? `Holding: ${integerFormat(holdingAmount)}` : ""} ${isFinite(amountAvailable) ? `\nAvailable: ${integerFormat(amountAvailable)}` : ""}`
        : "";
  }
  //inventory shortage is calculated based on holding amount
  row.inventoryShortage = getInventoryShortage(quantity, holdingAmount, portfolioRow?.assetAllocationsByBookType);
  return row;
}

export function getAmountAvailable(projectVintage: AdminProjectVintageResponse): number {
  if (!!projectVintage && projectVintage?.assetAllocationsByBookType) {
    const relevantBooks = projectVintage?.assetAllocationsByBookType?.filter((b) =>
      AllRubiconAvailableBookTypes.includes(b?.bookType),
    );

    return Decimal.sum(...relevantBooks.map((m) => m.totalAmountAvailable), 0).toNumber();
  }
  return 0;
}

export function getHoldingAmount(projectVintage: AdminProjectVintageResponse): number {
  if (!!projectVintage && projectVintage?.assetAllocationsByBookType) {
    const relevantBooks = projectVintage?.assetAllocationsByBookType?.filter((b) =>
      AllRubiconHoldingBookTypes.includes(b?.bookType),
    );

    return Decimal.sum(...relevantBooks.map((m) => m.totalAmountAllocated), 0).toNumber();
  }
  return 0;
}

export function fillMissingProjectInfo(
  projects: Project[],
  components: PortfolioComponentResponse[],
): PortfolioComponentResponse[] {
  if (projects?.length > 0 && components?.length > 0) {
    const updatedComponents = components.map((c) => {
      const project = projects.find((p) => p.id === c.projectId);
      if (!!project && !c.registryProjectId) {
        return {
          ...c,
          registryProjectId: project.registryProjectId,
          projectName: project.name,
          suspended: project?.suspended,
          rctStandard: project?.rctStandard,
          isScienceTeamApproved: project?.isScienceTeamApproved,
          integrityGradeScore: project?.integrityGradeScore,
        };
      } else {
        return {
          ...c,
          suspended: project?.suspended,
          rctStandard: project?.rctStandard,
          isScienceTeamApproved: project?.isScienceTeamApproved,
          integrityGradeScore: project?.integrityGradeScore,
        };
      }
    });
    return updatedComponents.sort((a, b) => a?.registryProjectId?.localeCompare(b?.registryProjectId));
  } else {
    return components;
  }
}
