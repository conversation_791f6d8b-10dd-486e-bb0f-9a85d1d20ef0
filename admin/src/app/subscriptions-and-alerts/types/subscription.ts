import { AlertEvent, NotificationCadence, NotificationEvent, PermissionEnum } from "@rubiconcarbon/shared-types";
import { UseControllerReturn } from "react-hook-form";

export type SubscriptionSectionType =
  | "purchase"
  | "retirement"
  | "portfolio"
  | "project"
  | "org and user"
  | "trade"
  | "book"
  | "activity"
  | "model portfolio";

export type NotificationEventPermissions = Partial<Record<NotificationEvent, PermissionEnum[]>>;
export type AlertEventPermissions = Partial<Record<AlertEvent, PermissionEnum[]>>;

export type SubscriptionSection = {
  label: string;
  subLabel: string;
  events: NotificationEvent[];
  cadences: NotificationCadence[];
  eventsSelected: NotificationEvent[];
  cadencesSelected: NotificationCadence[];
  permissions: NotificationEventPermissions;
  controller?: UseControllerReturn<SubscriptionModel, any>;
  alerts?: AlertSection[];
};
export type AlertSection = {
  label?: string; // descriptive label for when we want to add a description to the an alert section
  events: AlertEvent[];
  eventsSelected: AlertEvent[];
  permissions: AlertEventPermissions;
  controller?: UseControllerReturn<SubscriptionModel, any>;
};

export type NotificationSectionError = {
  events: boolean;
  cadences: boolean;
};

export type SuscriptionSections = Record<SubscriptionSectionType, SubscriptionSection>;

export type NotificationSectionsError = Record<SubscriptionSectionType, NotificationSectionError>;

export type SubscriptionModel = Record<
  SubscriptionSectionType,
  {
    eventsSelected: NotificationEvent[];
    cadencesSelected: NotificationCadence[];
    alerts?: {
      label?: string;
      permissions: AlertEventPermissions;
      events: AlertEvent[];
      eventsSelected: AlertEvent[];
    }[];
  }
>;
