import { Close, InfoRounded } from "@mui/icons-material";
import {
  SelectChangeEvent,
  Button,
  Popover,
  Stack,
  Typography,
  IconButton,
  Divider,
  FormControl,
  InputLabel,
  Select,
  Chip,
  MenuItem,
  Checkbox,
  ListItemText,
  Box,
  CircularProgress,
  Tooltip,
  ButtonGroup,
} from "@mui/material";
import { useState, useMemo, MouseEvent } from "react";
import { ReportApiValueToEnum, ReportEnum, ReportEnumToApiValue } from "../constants/report-enum";
import useSnackbarVariants from "@/utils/hooks/useEnqueueVariant";
import { ReportType, ReportTypeWithoutAll } from "../types/report";
import useHeadlessDownloader, { DownloadQuery } from "@/utils/hooks/useHeadlessDownloader";
import { Method } from "@/utils/types/api";
import { isEmptyObject } from "@/utils/helpers/general/data-structure";
import { useMeasure, useUpdateEffect } from "react-use";
import { LoadingButton } from "@mui/lab";
import Maybe from "@/components/maybe/maybe";
import useNavigationInterrupter from "@/utils/hooks/useNavigationInterrupter";
import GenericDialog from "@/components/ui/generic-dialog/generic-dialog";

import classes from "../styles/download-report.module.scss";

type ProgressFeedbackProps = {
  pending: number;
  total: number;
};

const Selects: ReportType[] = [ReportEnum.ALL, ReportEnum.HOLDING, ReportEnum.PORTFOLIO, ReportEnum.MARKET];

const ProgressFeedback = ({ pending, total }: ProgressFeedbackProps): JSX.Element => {
  const progress = useMemo(() => ((total - pending) / total) * 100, [pending, total]);

  return (
    <Box className={classes.ProgressFeedback}>
      <CircularProgress variant="indeterminate" value={progress} size={20} color="inherit" />
      <Box className={classes.ProgressFeedbackText}>
        <Typography variant="button">{pending}</Typography>
      </Box>
    </Box>
  );
};

const DownloadReport = (): JSX.Element => {
  const { enqueueSuccess, enqueueWarning, enqueueError } = useSnackbarVariants();
  const [selectContainer, { width: selectWidth }] = useMeasure();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [open, setOpen] = useState(false);
  const [selection, setSelection] = useState<ReportTypeWithoutAll[]>([]);
  const [triggerExport, setTriggerExport] = useState<boolean>(false);

  const id = useMemo(() => (open ? "popover" : undefined), [open]);
  const selectedApiTypes = useMemo(() => selection.map((type) => ReportEnumToApiValue[type]), [selection]);
  const downloadQuery = useMemo(
    () =>
      selectedApiTypes.reduce((accum: DownloadQuery, type) => {
        return {
          ...accum,
          [type]: { url: `reporting/report-download/${type}`, method: "GET" as Method },
        };
      }, {}),
    [selectedApiTypes],
  );

  const { downloading, results, progresses, reset } = useHeadlessDownloader({
    enable: triggerExport,
    query: downloadQuery,
    fileName: (key: string) => {
      return `${ReportApiValueToEnum[key]} (${new Date().toISOString().split(",")[0]}).xlsx`;
    },
  });

  const { show, setShow, continueNavigation } = useNavigationInterrupter(downloading);

  const pending = useMemo(() => Object.values(progresses).filter((value) => !!value).length, [progresses]);
  const total = selection.length;

  useUpdateEffect(() => {
    if (!downloading && !isEmptyObject(results)) {
      const allPassed = Object.values(results).every((value) => value.status === "success");
      const somePassed = Object.values(results).some((value) => value.status === "success");
      const allFailed = Object.values(results).every((value) => value.status === "error");
      const passed = Object.values(results).filter(({ status }) => status === "success").length;

      if (allPassed) enqueueSuccess(`Successfully exported report${total > 1 ? "s" : ""}`);
      else if (somePassed)
        enqueueWarning(`${passed} out of ${total} report${total > 1 ? "s were" : "was"} successfully exported`);
      else if (allFailed) enqueueError(`Unable to export report${total > 1 ? "s" : ""}`);

      reset();
    }
  }, [downloading, results]);

  useUpdateEffect(() => {
    if (!downloading) {
      setSelection([]);
      setTriggerExport(false);
    }
  }, [downloading]);

  const handleToggle = (event: MouseEvent<HTMLElement>): void => {
    event.preventDefault();

    setAnchorEl((anchorEl) => (anchorEl ? null : event.currentTarget));
    setOpen((open) => !open);
  };

  const handleSelectionChange = (event: SelectChangeEvent<ReportType[]>): void => {
    event.preventDefault();

    const values = event.target.value as ReportType[];

    if (values.includes(ReportEnum.ALL)) {
      if (values.length === 4) setSelection([]);
      else setSelection(Selects.slice(1) as ReportTypeWithoutAll[]);
    } else setSelection(values as ReportTypeWithoutAll[]);
  };

  const handleExport = (event: MouseEvent<HTMLElement>): void => {
    event.preventDefault();
    setTriggerExport(true);
    handleToggle(event);
  };

  return (
    <>
      <ButtonGroup
        component={Stack}
        direction="row"
        alignItems="center"
        classes={{
          firstButton: !downloading ? classes.FirstGroupButton : "",
        }}
      >
        <LoadingButton
          className={classes.DownloadReports}
          color="primary"
          variant="contained"
          disableElevation
          onClick={handleToggle}
          disabled={downloading}
          endIcon={
            <Maybe condition={downloading}>
              <ProgressFeedback pending={pending} total={total} />
            </Maybe>
          }
        >
          <Typography variant="button" sx={{ textTransform: "capitalize" }}>
            download{downloading ? "ing" : ""} report{pending > 1 ? "s" : ""}
          </Typography>
        </LoadingButton>
        <Maybe condition={downloading}>
          <Tooltip title={`${total - pending} out of ${total} downloaded`} enterTouchDelay={0}>
            <IconButton className={classes.InfoButton} disableRipple>
              <InfoRounded className={classes.InfoIcon} />
            </IconButton>
          </Tooltip>
        </Maybe>
      </ButtonGroup>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transitionDuration={400}
        classes={{
          paper: classes.DownloadReportsContent,
        }}
        onClose={handleToggle}
      >
        <Stack gap={1}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" p="0 10px">
            <Typography className={classes.DownloadReportsContentHeader}>Download reports</Typography>
            <IconButton onClick={handleToggle}>
              <Close />
            </IconButton>
          </Stack>
          <Divider />
          <Box className={classes.DownloadReportsSelectionContainer} ref={selectContainer}>
            <FormControl
              sx={{
                minWidth: 300,
                width: selectWidth - 40,
              }}
            >
              <InputLabel>Report type</InputLabel>
              <Select
                className={classes.Selection}
                id="report-type"
                name="Report type"
                label="Report type"
                multiple
                value={selection}
                renderValue={(selection) => (
                  <Stack direction="row" gap={0.5}>
                    {selection.map((select) => (
                      <Chip key={select} label={select} />
                    ))}
                  </Stack>
                )}
                onChange={handleSelectionChange}
              >
                {Selects.slice(0, 1).map((reportType) => (
                  <MenuItem key={reportType} value={reportType}>
                    <Checkbox checked={selection.length === 3} />
                    <ListItemText primary={reportType} />
                  </MenuItem>
                ))}
                <Divider />
                {Selects.slice(1).map((reportType) => (
                  <MenuItem key={reportType} value={reportType}>
                    <Checkbox checked={selection.includes(reportType as ReportTypeWithoutAll)} />
                    <ListItemText primary={reportType} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          <Stack
            className={classes.DownloadReportsContentActions}
            direction="row"
            justifyContent="flex-end"
            alignItems="center"
            gap={1}
          >
            <Button
              className={`${classes.DownloadSecondaryButton} ${classes.DownloadActionButton}`}
              disableElevation
              onClick={handleToggle}
            >
              cancel
            </Button>
            <Button
              className={`${classes.DownloadPrimaryButton} ${classes.DownloadActionButton}`}
              disableElevation
              color="primary"
              variant="contained"
              disabled={!selection.length}
              onClick={handleExport}
            >
              export
            </Button>
          </Stack>
        </Stack>
      </Popover>
      <GenericDialog
        open={show}
        title={`Interrupt download${pending > 1 ? "s" : ""}?`}
        positiveAction={{
          buttonText: "Interrupt anyway",
          actionType: "positive",
          onClick: continueNavigation,
        }}
        negativeAction={{
          buttonText: "No",
          actionType: "negative",
          onClick: () => setShow(false),
        }}
        onClose={() => setShow(false)}
        classes={{
          actions: classes.DialogActionButton,
        }}
      >
        <Typography className={classes.DialogContent}>
          The download{pending > 1 ? "s" : ""} may not successfully complete
        </Typography>
      </GenericDialog>
    </>
  );
};

export default DownloadReport;
