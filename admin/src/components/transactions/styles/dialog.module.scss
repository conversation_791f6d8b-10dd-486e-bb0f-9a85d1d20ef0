.Dialog {
    max-width: 650px;

    &.Binding {
        max-width: 850px;
    }

    &.FeeConfirmation {
        max-width: 1000px;
    }
}

.Title {
    background-color: #094436 !important;
    height: 70px;
    
    h2 {
        font-size: large;
    }

    * {
        color: white;
        font-weight: 400 !important;
    }
}

.StatusTitle {
    padding: 10px 25px !important;
}


.InterruptTitle {
    background-color: #C90005 !important;
}

.ClearTitle {
    background-color: transparent !important;
    * {
        color: rgb(48, 48, 48);
        font-weight: 500 !important;
    }
}

.Actions {
    background-color: rgb(248, 248, 248) !important;
    height: 70px;
    
    button {
        border-radius: 5px;
    }
}

.StatusActions {
    padding: 10px 25px !important;
    background-color: rgb(248, 248, 248) !important;
    height: 70px;
    
    button {
        border-radius: 5px;
    }
}

.Action {
    border-radius: 5px;
}

.InterruptPositiveAction {
    background-color: #C90005 !important;
}

.AddOption {
    max-width: 800px !important;

    .AddOptionContent {
        padding: 30px 20px !important;
    }
}

.HasEditsCallout {
    padding: 10px;
    border-radius: 5px;
    background-color: #EEEEEE;
    color: #000000;
}

@mixin CheckBoxFormControl {
    padding-left: 35px;

    label {
        span {
            // color: unset;
        }
    }

    .CheckboxText {
        font-weight: 300;
    }
}

.Approval {
    padding: 10px;
    border-radius: 5px;
    background-color: #FFFFFF;
    color: #000000;

    .CheckboxFormControl {
        @include CheckBoxFormControl;
    }
}

.FirmApproval {
    padding: 10px;
    border-radius: 5px;
    background-color: #E5F6FD;
    color: #000000;

    @include CheckBoxFormControl;
}

.NonApproval {
    padding: 10px;
    border-radius: 5px;
    background-color: #FDEDED;
    color: #000000;
}

.RadioGroup {
    padding-left: 25px;

    .Radio {
        font-size: small;
    }
}