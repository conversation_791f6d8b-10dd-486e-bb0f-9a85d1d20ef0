import { useAppRouter } from "@/utils/hooks/router-hooks";
import { Dispatch, SetStateAction, useCallback, useEffect, useState } from "react";

type UseNavigationInterrupterReturn = {
  show: boolean;
  setShow: Dispatch<SetStateAction<boolean>>;
  continueNavigation: () => void;
};

// Note: This hook may not work as expected with App Router
// App Router doesn't have the same events system as Pages Router
// This is a placeholder implementation that will need to be updated
const useNavigationInterrupter = (uploading: boolean): UseNavigationInterrupterReturn => {
  const router = useAppRouter();
  const [navigationUrl, setNavigationUrl] = useState<string>("");
  const [showNavigationModal, setShowNavigationModal] = useState<boolean>(false);

  // App Router doesn't have the same events system as Pages Router
  // This is a placeholder implementation that will need to be updated
  const continueNavigation = useCallback(() => {
    if (navigationUrl) {
      setShowNavigationModal(false);
      router.push(navigationUrl);
    }
  }, [navigationUrl, router]);

  return {
    show: showNavigationModal,
    setShow: setShowNavigationModal,
    continueNavigation,
  };
};

export default useNavigationInterrupter;
