import { Stack, Tooltip, Typography } from "@mui/material";
import Decimal from "decimal.js";
import classes from "./styles.module.scss";
import ArrowDropUpIcon from "@mui/icons-material/ArrowDropUp";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import Maybe from "@/components/maybe/maybe";

const MarketIndexDisplay = (props: {
  label: string;
  percent: Decimal;
  price?: Decimal;
  tooltip?: string;
}): JSX.Element => {
  const { label, percent, price = null, tooltip = "" } = props;

  if (percent === null) return;
  const percentInput = new Decimal(percent);
  const percentValue = percentInput.times(100);
  return (
    <Stack direction="row" gap={1} alignItems="center" display="inline-flex" sx={{ width: "100%" }}>
      <Tooltip title={tooltip}>
        <Typography variant="body2" component="div" className={classes.label}>
          {label}
        </Typography>
      </Tooltip>
      <Typography
        display={"inline-flex"}
        variant="body2"
        component="div"
        className={
          percentValue.isNegative() ? classes.negativeValue : percentValue.isZero() ? "" : classes.positiveValue
        }
      >
        {percentValue.absoluteValue().toFixed(2, Decimal.ROUND_HALF_UP)}%
        <Maybe condition={!percentValue.isZero()}>
          {percentValue.isNegative() ? <ArrowDropDownIcon /> : <ArrowDropUpIcon />}
        </Maybe>
      </Typography>
      <Maybe condition={!!price}>
        <Typography variant="body2" component="div" className={""}>
          ${price?.toFixed(2, Decimal.ROUND_HALF_UP)}
        </Typography>
      </Maybe>
      <Typography variant="body2" component="div" className={classes.separator}>
        {`|`}
      </Typography>
    </Stack>
  );
};

export default MarketIndexDisplay;
