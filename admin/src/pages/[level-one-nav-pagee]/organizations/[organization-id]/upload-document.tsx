import Page from "@/components/layout/containers/page";
import { PermissionEnum } from "@rubiconcarbon/shared-types";
import { Authorize } from "@/services/authorize";
import { useRouter } from "next/router";
import { Maybe } from "@rubiconcarbon/frontend-shared";
import DocumentUpload from "@/components/organizations/components/document-upload";
import { OrganizationType } from "@/constants/organization-type.enum";

export default function DocumentUploadPage(): JSX.Element {
  const { query, isReady } = useRouter();

  return (
    <Maybe condition={isReady}>
      <Authorize permissions={[PermissionEnum.DOCUMENTS_CREATE]}>
        <Page>
          <DocumentUpload
            id={query["organization-id"] as string}
            type={query["type"]?.toString() as OrganizationType}
          />
        </Page>
      </Authorize>
    </Maybe>
  );
}
