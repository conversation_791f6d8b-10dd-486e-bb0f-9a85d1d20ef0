import { PrimitiveTypeProject } from "@/models/primitive-type-project";
import { uuid, TrimmedProjectVintageResponse, TrimmedOrganizationResponse } from "@rubiconcarbon/shared-types";
import { Type } from "class-transformer";
import { IsNotEmpty, Min, ValidateIf, ValidateNested } from "class-validator";

class OrganizationAndMemo implements TrimmedOrganizationResponse {
  @IsNotEmpty({ message: "Required if memo is empty." })
  @ValidateIf((o) => !o.memo)
  id: uuid;

  name: string;

  @IsNotEmpty({ message: "Required if customer is empty." })
  @ValidateIf((o) => !o?.id)
  memo?: string;

  createdAt: Date;
}

class Vintage implements TrimmedProjectVintageResponse {
  @IsNotEmpty({ message: "Required." })
  id: uuid;

  name: string;

  @Type(() => PrimitiveTypeProject)
  @ValidateNested()
  project?: PrimitiveTypeProject; // @kofi had to make this optional to get it to build in index.ts

  isRctEligible: boolean;

  createdAt: Date;
}

export class ReserveModel {
  constructor() {}

  id?: uuid = null;

  @Type(() => Vintage)
  @ValidateNested()
  projectVintage: Vintage = null;

  @Type(() => Number)
  @Min(1, { message: "Amount must be greater than 0." })
  @IsNotEmpty({ message: "Required." })
  amount: number = null;

  @Type(() => OrganizationAndMemo)
  @ValidateNested()
  organizationAndMemo: OrganizationAndMemo = null;

  amountAvailable?: number = null;

  updatedAt?: Date = null;

  currentEditAllocated?: number = null;
}

export class ReserveFormModel {
  constructor() {}

  @ValidateNested()
  @Type(() => ReserveModel)
  amends: ReserveModel[];
}
