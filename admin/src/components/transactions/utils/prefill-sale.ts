import { PrimitiveTypeBook2 } from "@/models/primitive-type-book";
import { AssetOrder, NonTradeAssetDetails } from "@/models/transaction";
import {
  AdminBookResponse,
  AllocationResponse,
  GroupedAllocationWithNestedResponse,
  ModelPortfolioResponse,
} from "@rubiconcarbon/shared-types";
import { isEmpty } from "lodash";
import { bookSelectionOrder } from "../constants/sale-prefill";

export function buildProduct(
  allocation: AllocationResponse,
  selectedBook: AdminBookResponse,
  amount: number,
): AssetOrder {
  if (!!allocation) {
    const asset = new AssetOrder();
    asset.isAPurchase = true;
    asset.isASale = true;
    asset.amount = amount.toString();
    const selectedBookSource: PrimitiveTypeBook2 = {
      id: selectedBook.id,
      name: selectedBook.name,
      type: selectedBook.type,
      limit: null,
    };
    asset.source = selectedBookSource;
    const supplementaryAssetDetails = new NonTradeAssetDetails();
    supplementaryAssetDetails.id = allocation?.asset?.id;
    supplementaryAssetDetails.type = "vintage";
    supplementaryAssetDetails.name = allocation?.asset?.name;
    supplementaryAssetDetails.sourceName = allocation?.owner?.name;
    supplementaryAssetDetails.available = allocation?.amountAvailable;
    supplementaryAssetDetails.projectName = allocation?.detailedAsset?.["project"]?.["name"];
    supplementaryAssetDetails.registryProjectId = allocation?.asset?.registryProjectId;
    supplementaryAssetDetails.wholeAsset = allocation?.detailedAsset;
    supplementaryAssetDetails.sourceId = allocation?.owner?.id;
    supplementaryAssetDetails.sourceType = allocation?.owner?.type;
    supplementaryAssetDetails.sourceName = allocation?.owner?.name;
    asset.supplementaryAssetDetails = supplementaryAssetDetails;

    return asset;
  }
  return null;
}

export function buildPrefilledProducts(mockBasket: ModelPortfolioResponse, books: AdminBookResponse[]): AssetOrder[] {
  const products: AssetOrder[] = [];
  if (!!mockBasket && !isEmpty(books)) {
    mockBasket?.modelPortfolioComponents?.forEach((mp) => {
      let bookSelectionIdx = 0;
      let amountToFill = mp.amountAllocated ?? 0;
      while (bookSelectionIdx < bookSelectionOrder.length && amountToFill > 0) {
        const selectedBook: AdminBookResponse = books.find(
          (book) => book.type === bookSelectionOrder[bookSelectionIdx].bookType,
        );

        const selectedAllocation = (
          selectedBook?.ownerAllocations as GroupedAllocationWithNestedResponse
        ).allocations?.find(
          (allocation) =>
            allocation?.owner?.type === bookSelectionOrder[bookSelectionIdx].ownerType &&
            allocation?.detailedAsset.id === mp.vintageId,
        );

        if (!!selectedAllocation) {
          const assetAmount =
            selectedAllocation.amountAvailable < amountToFill ? selectedAllocation.amountAvailable : amountToFill;
          amountToFill = amountToFill - selectedAllocation.amountAvailable;
          products.push(buildProduct(selectedAllocation, selectedBook, assetAmount));
        }

        bookSelectionIdx++;
      }
    });
  }
  return products;
}
